div
<template>
  <div class="exam-paper-action-container">
    <div class="container-header">
      <div class="left">
        <span>试卷--{{ typeMap[type].label }}</span>
      </div>
      <div class="right">
        <div class="btn primary-btn" @click="handleBack">返回</div>
      </div>
    </div>
    <div class="content">
      <div class="left-content">
        <div class="exam-paper-form">
          <div class="block">
            <div class="label-title">已选题目</div>
            <div class="label-tips">从题库中选择试卷试题。</div>
            <div class="input-item">
              <div class="label">已选</div>
              <div class="input">
                <el-input
                  v-model="examPaperForm.count"
                  placeholder="请选择题目"
                  clearable
                  size="large"
                  class="input-with-inner-append"
                  readonly
                >
                  <template #append>题</template>
                </el-input>
              </div>
            </div>
          </div>
          <div class="block">
            <div class="label-title">题目分值</div>

            <div
              class="input-item"
              v-for="(item, index) in questionOptions"
              :key="index"
            >
              <div class="label">
                {{ item.label }}：已选{{ item.count }} 题，每题分值
              </div>
              <div class="input">
                <el-input
                  v-model="item.score"
                  placeholder="请输入"
                  clearable
                  size="large"
                  class="input-with-inner-append"
                >
                  <template #append>分</template>
                </el-input>
              </div>
            </div>
            <div class="input-item">
              <div class="label">满分：</div>
              <div class="input">
                <el-input
                  v-model="examPaperForm.total_score"
                  placeholder="请输入"
                  clearable
                  size="large"
                  class="input-with-inner-append"
                >
                  <template #append>分</template>
                </el-input>
              </div>
            </div>
          </div>
        </div>

        <div class="footer">
          <div class="left">
            <div class="text-btn" @click="handleQuestions">
              <i-ep-circle-plus style="margin-right: 5px" />
              添加题目
            </div>
          </div>
          <div class="right">
            <div class="btn cancel-btn" @click="handleBack">取 消</div>
            <div class="btn primary-btn" @click="handleSubmit">保 存</div>
          </div>
        </div>
      </div>
      <div class="right-content">
        <el-scrollbar
          style="height: 100%; width: 100%"
          warp-style="overflow-x: hidden;"
          class="exam-paper-scrollbar"
        >
          <div class="exam-paper-container">
            <ExamPaper
              :isOperable="false"
              :showAnswer="true"
              :showSelectedAnswer="false"
              :showGetScore="false"
              :showQuestionScore="false"
              :showCheckBox="false"
              :itemStyle="'shadowBorder'"
              :showAnswerTime="false"
              :showAnalysis="false"
              :showAnswerType="'all'"
              @handle-action="handleQuestionAction"
            />
          </div>
        </el-scrollbar>
      </div>
    </div>

    <!-- 题目选择弹窗 -->
    <el-dialog
      class="questionBankDialog"
      v-model="questionDialog.visible"
      :title="questionDialog.title"
      :width="questionDialog.width"
      append-to-body
      @close="closeQuestionDialog"
    >
      <template #header>
        <div class="dialog-title">
          {{ questionDialog.title }}
        </div>
      </template>
      <div class="dialog-body">
        <div class="dialog-content-header">
          <div class="filter-row">
            <el-select
              v-model="questionQueryParams.topic_id"
              placeholder="题库"
              clearable
              size="large"
            >
              <el-option
                v-for="item in questionBankOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </div>
          <div class="filter-row">
            <el-input
              v-model="questionQueryParams.search"
              placeholder="题目"
              clearable
              size="large"
            />
          </div>
          <div class="btn primary-btn" @click="handleQuestionQuery">
            <i-ep-search /> 搜索
          </div>
        </div>

        <div class="selection-controls">
          <div
            class="left-controls custom-checkbox-rectangle checkbox-rectangle-green"
          >
            <el-checkbox
              v-model="selectAll"
              :indeterminate="isIndeterminate"
              @change="handleSelectAll"
            >
              全选
            </el-checkbox>
            <el-checkbox v-model="selectNone" @change="handleSelectNone">
              全不选
            </el-checkbox>
          </div>
        </div>

        <div class="dialog-content-table">
          <el-scrollbar
            style="height: 100%; width: 100%"
            warp-style="overflow-x: hidden;"
          >
            <div class="question-list">
              <!-- 使用已有的题目组件来渲染题目内容 -->
              <ExamPaper
                :paperData="formatQuestionsForPaper(questionTableList)"
                :isOperable="false"
                :showAnswer="true"
                :showSelectedAnswer="false"
                :showGetScore="false"
                :showQuestionScore="false"
                :showCheckBox="true"
                :itemStyle="'shadowBorder'"
                :showAnswerTime="false"
                :showAnalysis="false"
                :showAnswerType="'all'"
                @handle-action="handleQuestionAction"
              />
            </div>
          </el-scrollbar>
        </div>

        <div class="dialog-content-footer">
          <pagination
            v-if="questionTotal > 0"
            v-model:total="questionTotal"
            v-model:page="questionQueryParams.pageNum"
            v-model:limit="questionQueryParams.pageSize"
            @pagination="getQuestionData"
          />
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <div class="left">
            <span>已选{{ selectedQuestions?.length || 0 }}道题目</span>
          </div>
          <div class="right">
            <div class="btn cancel-btn" @click="closeQuestionDialog">取 消</div>
            <div class="btn primary-btn" @click="handleSubmitQuestions">
              确 定
            </div>
          </div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { useRoute, useRouter } from "vue-router";
import ExamPaper from "./components/ExamPaper.vue";

import { getExamPaperDetail, addExamPaper, updateExamPaper } from "@/api/exam";

defineOptions({
  name: "ExamPaperAction",
  inheritAttrs: false,
});

const route = useRoute();
const router = useRouter();

const type: any = route.query.type;
const examPaperId: any = route.query.id;
const typeMap = reactive<any>({
  create: { value: "create", label: "新增" },
  edit: { value: "edit", label: "修改" },
  detail: { value: "detail", label: "详情" },
});
const examPaperDetail = reactive<any>({});
const examPaperForm = reactive<any>({
  // id	:'',//Integer	记录id
  name: "", //String	试卷名称
  remark: "", //String	试卷说明
  time_limit: "", //	Integer	试卷限时，单位：分钟
  scores: "", //	Integer	总分
  questions: [], //Array	试题列表
});
const questionsData = ref<any>([
  {
    id: "", //	Integer	试题id
    name: "", //	String	试题题目
    scores: "", //	Integer	得分
    q_type: "", //Integer	试题类型，10-单选，20-多选，30-填空，40-判断
    content: "", //	String	题目内容，当题目类型为选择题（p_type=10和20）时，为json格式的选项内容，如：[{"A": "xxx"}, {"B": "xxx"}]
    answers: "", //String	题目的答案，有多个时用逗号隔开，如："A, B"
    remark: "", //String	提示解析
  },
]);

const questionOptions = ref<any>([
  { label: "单选题", q_type: 10, count: 4, score: 10 },
  { label: "多选题", q_type: 20, count: 2, score: 10 },
  { label: "填空题", q_type: 30, count: 1, score: 10 },
  { label: "判断题", q_type: 40, count: 1, score: 10 },
]);
const questionMap = reactive<any>({
  10: { label: "单选" },
  20: { label: "多选" },
  30: { label: "填空" },
  40: { label: "判断" },
});
const questionDialog = reactive<any>({
  visible: false,
  width: "50%",
  title: "选择题目",
});

const questionQueryParams = reactive<any>({
  search: "",
  topic_id: "",
  pageNum: 1,
  pageSize: 10,
});

const questionTableList = ref<any>([]);
const questionTotal = ref(0);
const questionLoading = ref(false);
const selectedQuestions = ref<any>([]);
const selectAll = ref(false);
const selectNone = ref(false);

const questionBankOptions = ref<any>([
  { value: "", label: "全部题库" },
  { value: "1", label: "安全知识题库" },
  { value: "2", label: "操作规范题库" },
  { value: "3", label: "应急处理题库" },
]);

const isIndeterminate = computed(() => {
  const selectedCount = selectedQuestions.value.length;
  const totalCount = questionTableList.value.length;
  return selectedCount > 0 && selectedCount < totalCount;
});

onBeforeMount(() => {});
onMounted(() => {
  getPaperData();
});

function getPaperData() {
  if (examPaperId) {
    getExamPaperDetail(examPaperId).then((res: any) => {
      Object.assign(examPaperForm, res);
    });
  }
}

//题目选择弹窗方法

async function getQuestionData() {
  questionLoading.value = true;
  try {
    // 这里应该调用实际的API
    // const params={
    //   search:questionQueryParams.search,
    //   topic_id:questionQueryParams.topic_id,
    //   pageNum:questionQueryParams.pageNum,
    //   pageSize:questionQueryParams.pageSize
    // }
    // const response = await getQuestions(params);
    // questionTableList.value = response.data.list;
    // questionTotal.value = response.data.total;
    const mockData = {
      list: [
        {
          id: 1,
          title: "中央空调开机前应该（）的先后顺序，使机组投入运行",
          q_type: 10,
          score: 5,
          options: [
            "压缩机、冷冻泵、冷却泵、冷却塔",
            "冷冻泵、冷却泵、冷却塔风机、压缩机",
            "压缩机、冷却泵、冷冻泵、冷却塔",
          ],
          correct_answer: "B",
          explanation: "正确的开机顺序应该是冷冻泵、冷却泵、冷却塔风机、压缩机",
        },
        {
          id: 2,
          title: "中央空调系统中，冷却水的作用是将（）带走",
          q_type: 30,
          score: 5,
          correct_answer: "热量",
          explanation: "冷却水的主要作用是将冷凝器产生的热量带走",
        },
        {
          id: 3,
          title: "以下哪些是中央空调系统的主要组成部分？",
          q_type: 20,
          score: 8,
          options: ["压缩机", "冷凝器", "蒸发器", "节流装置", "风扇"],
          correct_answer: "A,B,C,D",
          explanation: "中央空调系统主要由压缩机、冷凝器、蒸发器、节流装置组成",
        },
        {
          id: 4,
          title: "中央空调系统运行时需要定期检查冷却水温度",
          q_type: 40,
          score: 3,
          correct_answer: "A",
          explanation: "定期检查冷却水温度是保证系统正常运行的重要措施",
        },
      ],
      total: 4,
    };

    questionTableList.value = mockData.list;
    questionTotal.value = mockData.total;
  } catch (error) {
    console.error("获取题目数据失败:", error);
  } finally {
    questionLoading.value = false;
  }
}
function handleQuestions() {
  questionDialog.visible = true;
  getQuestionData();
}

function closeQuestionDialog() {
  questionDialog.visible = false;
  selectedQuestions.value = [];
  selectAll.value = false;
  selectNone.value = false;
  questionQueryParams.search = "";
  questionQueryParams.topic_id = "";
  questionQueryParams.pageNum = 1;
}

function handleQuestionQuery() {
  questionQueryParams.pageNum = 1;
  getQuestionData();
}

function isQuestionSelected(questionId: any) {
  return selectedQuestions.value.some((q: any) => q.id === questionId);
}

function handleQuestionSelect(question: any, checked: boolean) {
  if (checked) {
    if (!isQuestionSelected(question.id)) {
      selectedQuestions.value.push(question);
    }
  } else {
    const index = selectedQuestions.value.findIndex(
      (q: any) => q.id === question.id
    );
    if (index > -1) {
      selectedQuestions.value.splice(index, 1);
    }
  }
  updateSelectAllState();
}

function handleSelectAll(checked: boolean) {
  if (checked) {
    selectedQuestions.value = [...questionTableList.value];
    selectNone.value = false;
  } else {
    selectedQuestions.value = [];
  }
}

function handleSelectNone(checked: boolean) {
  if (checked) {
    selectedQuestions.value = [];
    selectAll.value = false;
  }
}

function updateSelectAllState() {
  const selectedCount = selectedQuestions.value.length;
  const totalCount = questionTableList.value.length;
  selectAll.value = selectedCount === totalCount && totalCount > 0;
  selectNone.value = selectedCount === 0;
}

function formatQuestionsForPaper(questions: any[]) {
  // 将题目数据格式化为ExamPaper组件需要的格式
  return questions.map((question: any) => ({
    id: question.id,
    category: question.q_type,
    title: getQuestionTypeText(question.q_type),
    score: question.score || 5,
    questionNum: 1,
    answers: question.correct_answer,
    content: {
      id: question.id,
      category: question.q_type,
      title: question.title,
      options: formatOptionsForPaper(question),
      answers: question.correct_answer,
      remark: question.explanation || "",
    },
  }));
}

function formatOptionsForPaper(question: any) {
  if (question.q_type === 10 || question.q_type === 20) {
    // 单选题或多选题
    if (question.options && Array.isArray(question.options)) {
      return question.options.map((option: any, index: number) => {
        const key = getOptionLabel(index);
        return { [key]: option.text || option };
      });
    }
  } else if (question.q_type === 40) {
    // 判断题
    return [{ A: "正确" }, { B: "错误" }];
  }
  return [];
}

function getQuestionTypeText(type: number) {
  const typeMap: any = {
    10: "单选题",
    20: "多选题",
    30: "填空题",
    40: "判断题",
  };
  return typeMap[type] || "未知";
}

function getOptionLabel(index: number) {
  return String.fromCharCode(65 + index);
}

function handleQuestionAction(payload: any) {
 

  if (payload.type === "select-question") {
    // 处理选择题目
    const question =
      payload.chosenQuestions[payload.chosenQuestions.length - 1];
    if (question && !isQuestionSelected(question.content.id)) {
      selectedQuestions.value.push({
        id: question.content.id,
        title: question.content.title,
        q_type: question.content.category,
        score: question.score || 5,
        options: question.content.options || [],
        correct_answer: question.content.answers,
        explanation: question.content.remark || "",
      });
    }
  } else if (payload.type === "remove-question") {
    // 处理取消选择题目
    const question = payload.chosenQuestion;
    if (question) {
      const index = selectedQuestions.value.findIndex(
        (q: any) => q.id === question.content.id
      );
      if (index > -1) {
        selectedQuestions.value.splice(index, 1);
      }
    }
  }

  updateSelectAllState();
}

function handleSubmitQuestions() {
  if (selectedQuestions.value.length === 0) {
    ElMessage.warning("请至少选择一道题目");
    return;
  }

  // 获取选中的题目ID列表
  const selectedQuestionIds = selectedQuestions.value.map((q: any) => q.id);
  console.log("选中的题目ID:", selectedQuestionIds);

  // 添加题目到试卷
  selectedQuestions.value.forEach((question: any) => {
    const existingIndex = examPaperForm.questions.findIndex(
      (q: any) => q.id === question.id
    );
    if (existingIndex === -1) {
      examPaperForm.questions.push({
        id: question.id,
        title: question.title,
        q_type: question.q_type,
        score: question.score || 5,
        options: question.options || [],
        correct_answer: question.correct_answer,
        explanation: question.explanation || "",
        seq: examPaperForm.questions.length + 1,
      });
    }
  });

  // 更新试卷总分
  updatePaperTotalScore();

  ElMessage.success(`成功添加${selectedQuestions.value.length}道题目`);

  closeQuestionDialog();
}

function updatePaperTotalScore() {
  // 计算试卷总分
  const totalScore = examPaperForm.questions.reduce(
    (sum: number, question: any) => {
      return sum + (question.score || 5);
    },
    0
  );
  examPaperForm.scores = totalScore;
}

function handleSubmit() {
  const data: any = {};
  ElMessageBox.confirm("是否确认操作?", "提示", {
    confirmButtonText: "是",
    cancelButtonText: "否",
    type: "warning",
  }).then(() => {
    if (type === "create") {
      addExamPaper(data).then((res: any) => {
        if (res.status == 200) {
          ElMessage.success({
            message: "新增成功!",
          });
          handleBack();
        }
      });
    } else if (type === "edit") {
      updateExamPaper(examPaperId, data).then((res: any) => {
        if (res.status == 200) {
          ElMessage.success({
            message: "修改成功!",
          });
          handleBack();
        }
      });
    }
  });
}
function handleBack() {
  router.go(-1);
}
</script>

<style scoped lang="scss">
.exam-paper-action-container {
  height: 95%;
  margin: 20px;

  .container-header {
    display: flex;
    justify-content: space-between;
    padding: 20px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 3px 20px 1px rgb(109 125 126 / 7%);
    .left {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 20px;
      font-weight: 500;
      color: #3b4664;

      .tips {
        margin-left: 30px;
        font-size: 14px;
        font-weight: 400;
        color: #f23c33;
      }
    }

    .right {
      .btn {
        width: 52px;
        height: 28px;
        font-size: 13px;
        font-weight: 400;
        border-radius: 13px;
      }
    }
  }
  .content {
    width: 100%;
    height: calc(100% - 70px);
    padding: 10px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .left-content {
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 3px 20px 1px rgb(109 125 126 / 7%);
      width: 487px;
      padding: 33px 25px;
      .exam-paper-form {
        .label-title {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          font-weight: 500;
          font-size: 18px;
          color: #3b4664;
        }
        .label-tips {
          font-weight: 400;
          font-size: 15px;
          color: #3b4664;
          margin-top: 15px;
        }
        .label {
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 20px;
          font-size: 15px;
          font-weight: 400;
          color: #3b4664;

          span {
            color: red;
          }
        }
        .input {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          flex: 1;

          span {
            margin-top: 10px;
            font-size: 13px;
            font-weight: 400;
            color: #8d9295;
            text-align: left;
          }
        }

        .block {
          display: flex;
          flex-direction: column;
          margin-bottom: 20px;
          .input-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-top: 10px;
          }
        }
      }
      .footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20px;

        .left {
          display: flex;
          align-items: center;
          .text-btn {
            font-size: 16px;
            font-weight: 400;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            svg {
              font-size: 28px;
            }
          }
        }
        .right {
          display: flex;
          align-items: center;
          justify-content: flex-end;
          .btn {
            width: 96px;
            height: 42px;
            border-radius: 10px;
            margin-left: 10px;
            &:nth-child(1) {
              margin-left: 0;
            }
          }
        }
      }
    }
    .right-content {
      width: calc(100% - 500px);
      height: 100%;
      background: #fff;
      display: flex;
      flex-direction: column;

      border-radius: 8px;
      box-shadow: 0 3px 20px 1px rgb(109 125 126 / 7%);

      .exam-scrollbar,
      .el-scrollbar__wrap {
        width: 100%;
      }
      :deep(.el-scrollbar__view) {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .paper-info {
        width: 95%;
      }
    }
  }
}
</style>
<style lang="scss">
.questionBankDialog {
  padding: 0 !important;

  .el-dialog__header {
    height: 88px;
    text-align: center;
    // background: url("@/assets/images/dialog-header-green2.png") no-repeat;
    // background-size: 100% 100%;

    .dialog-title {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      font-size: 24px;
      font-weight: 500;
      // color: #fff;
    }
  }

  .el-dialog__body {
    padding: 0 20px 20px 20px !important;
    height: 70vh;
  }

  .el-dialog__footer {
    padding: 20px !important;
  }

  // .el-dialog__close {
  //   width: 21px;
  //   height: 21px;
  //   background: #fff;
  //   border-radius: 50%;
  //   svg {
  //     font-size: 20px;
  //     path {
  //       fill: #00918c !important;
  //     }
  //   }
  // }

  .dialog-body {
    height: 100%;
  }

  .dialog-content-header {
    margin-bottom: 20px;
    display: flex;
    align-items: center;

    .filter-row {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 260px;
      margin-left: 20px;

      &:nth-child(1) {
        width: 260px;
        margin-left: 0;
      }
    }
    .btn {
      width: 116px;
      height: 40px;
      margin-left: 20px;
    }
  }

  .selection-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 20px;

    .left-controls {
      display: flex;
      gap: 20px;
    }

    .right-info {
      font-size: 14px;
      color: #666;
      font-weight: 500;
    }
  }

  .dialog-content-table {
    height: calc(100% - 180px);
    margin-bottom: 20px;

    .question-list {
      height: 100%;
      .question-item {
        margin-bottom: 15px;
        .question-content {
          display: flex;
          align-items: flex-start;
          padding: 20px;
          gap: 15px;
        }
      }
    }
  }

  .dialog-content-footer {
    display: flex;
    justify-content: center;
    padding: 10px 0;
  }

  .dialog-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .left {
      display: flex;
      align-items: center;
      span {
        padding: 2px 0;
        font-size: 20px;
        font-weight: 500;
        color: #00918c;
      }
    }

    .right {
      display: flex;
      align-items: center;
    }
  }
}
</style>
