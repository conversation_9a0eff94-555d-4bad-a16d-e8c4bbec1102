<template>
  <div class="question-item">
    <!-- 题目标题 -->
    <div class="question-header">
      <div class="question-header-left">
        <div class="label">
          {{ questionTypeLabel }}
        </div>
        <div class="input">
          <el-input
            v-model="localData.title"
            placeholder="请输入题目 "
            type="textarea"
            maxlength="500"
            show-word-limit
            size="large"
            :rows="2"
          />
        </div>
      </div>
      <div class="question-actions">
        <div class="drag-sort">
          <svg-icon icon-class="drag3" />
        </div>
        <div class="delete-icon" @click="handleDelete">
          <svg-icon icon-class="delete" />
        </div>
      </div>
    </div>

    <!-- 题目图片 -->
    <div class="question-img-list">
      <!-- 上传按钮 -->
      <div
        class="img-upload"
        v-if="!localData.images || localData.images.length < 6"
      >
        <svg-icon icon-class="default-img" @click="triggerImageUpload" />
        <div class="img-tips">
          上传图片，题目图片上限 6 张，每张不可超过 2 Mb
        </div>
      </div>

      <div
        class="upload-list"
        v-if="localData.images && localData.images.length > 0"
      >
        <!-- 已上传的图片 -->
        <div
          class="img-item"
          v-for="(img, imgIndex) in localData.images"
          :key="imgIndex"
        >
          <img :src="img.url" :alt="`图片${imgIndex + 1}`" />
          <div class="img-delete" @click="removeImage(imgIndex)">
            <svg-icon icon-class="close2" />
          </div>
        </div>
      </div>

      <!-- 隐藏的文件上传输入框 -->
      <input
        ref="imageUploadRef"
        type="file"
        accept="image/*"
        multiple
        style="display: none"
        @change="handleImageUpload"
      />
    </div>

    <!-- 题目编辑表单 -->
    <div class="question-form">
      <!-- 选项 -->
      <div class="question-options">
        <div class="label">
          {{
            localData.q_type == 30 || localData.q_type == 40 ? " 答案" : "选项"
          }}
          <i-ep-circle-plus
            @click="addOption"
            v-if="
              localData.options &&
              localData.options.length < 6 &&
              (localData.q_type == 10 || localData.q_type == 20)
            "
          />
          <!-- 单选和多选才可以添加选项 -->
        </div>
        <div class="green-block">
          <!-- 单选 -->
          <template v-if="localData.q_type == 10">
            <div class="options-container">
              <div
                class="option-item"
                v-for="(option, optIndex) in localData.options"
                :key="optIndex"
              >
                <div
                  class="option-label custom-checkbox-circle checkbox-circle-green2"
                >
                  <svg-icon icon-class="default-img" />
                  <el-checkbox
                    :model-value="
                      localData.correctAnswer ===
                      String.fromCharCode(65 + optIndex)
                    "
                    @change="(checked) => handleSingleChoice(checked, optIndex)"
                  />
                  <span class="option-letter"
                    >{{ String.fromCharCode(65 + optIndex) }}.</span
                  >
                </div>
                <div class="input">
                  <el-input
                    v-model="option.text"
                    placeholder="请输入答案"
                    type="text"
                    maxlength="100"
                    show-word-limit
                    size="large"
                    resize="none"
                    clearable
                  />
                </div>
                <div
                  class="delete-icon"
                  @click="removeOption(optIndex)"
                  v-if="localData.options.length > 2"
                >
                  <svg-icon icon-class="delete" />
                </div>
              </div>
            </div>
          </template>

          <!-- 多选 -->
          <template v-if="localData.q_type == 20">
            <div class="options-container">
              <div
                class="option-item"
                v-for="(option, optIndex) in localData.options"
                :key="optIndex"
              >
                <div
                  class="option-label custom-checkbox-circle checkbox-circle-green2"
                >
                  <svg-icon icon-class="default-img" />
                  <el-checkbox
                    :model-value="
                      localData.correctAnswers &&
                      localData.correctAnswers.includes(
                        String.fromCharCode(65 + optIndex)
                      )
                    "
                    @change="(checked: boolean) => handleMultipleChoice(checked, optIndex)"
                  />

                  <span class="option-letter"
                    >{{ String.fromCharCode(65 + optIndex) }}.</span
                  >
                </div>
                <div class="input">
                  <el-input
                    v-model="option.text"
                    placeholder="请输入答案"
                    type="text"
                    maxlength="100"
                    show-word-limit
                    size="large"
                    resize="none"
                    clearable
                  />
                </div>
                <div
                  class="delete-icon"
                  @click="removeOption(optIndex)"
                  v-if="localData.options.length > 2"
                >
                  <svg-icon icon-class="delete" />
                </div>
              </div>
            </div>
          </template>

          <!-- 填空 -->
          <template v-if="localData.q_type == 30">
            <div class="options-container">
              <div class="option-item">
                <div class="option-label">
                  {{ localData.correctAnswer }}
                </div>
                <div class="input">
                  <el-input
                    v-model="localData.correctAnswer"
                    placeholder="请输入答案"
                    type="text"
                    maxlength="100"
                    show-word-limit
                    size="large"
                    resize="none"
                    clearable
                  />
                </div>
              </div>
            </div>
          </template>

          <!-- 判断 -->
          <template v-if="localData.q_type == 40">
            <div class="options-container">
              <div class="option-item">
                <div
                  class="option-label custom-checkbox-circle checkbox-circle-green2"
                >
                  <svg-icon icon-class="default-img" />
                  <el-checkbox
                    :model-value="localData.correctAnswer === 'A'"
                    @change="(checked: boolean) => handleJudgmentChoice(checked, 'A')"
                  />
                  <span class="option-letter">A. 正确</span>
                </div>
              </div>
              <div class="option-item">
                <div
                  class="option-label custom-checkbox-circle checkbox-circle-green2"
                >
                  <svg-icon icon-class="default-img" />
                  <el-checkbox
                    :model-value="localData.correctAnswer === 'B'"
                    @change="(checked: boolean) => handleJudgmentChoice(checked, 'B')"
                  />
                  <span class="option-letter">B. 错误</span>
                </div>
              </div>
            </div>
          </template>
        </div>
      </div>

      <!-- 解析 -->
      <div class="question-analysis">
        <div class="label">解析</div>
        <div class="green-block">
          <div class="input">
            <el-input
              v-model="localData.remark"
              placeholder="请输入解析"
              type="textarea"
              maxlength="500"
              show-word-limit
              size="large"
              :rows="2"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ElMessage } from "element-plus";

defineOptions({
  name: "QuestionItem",
  inheritAttrs: false,
});

const props = defineProps({
  questionData: {
    type: Object,
    required: true,
  },
  questionIndex: {
    type: Number,
    required: true,
  },
  questionTypeMap: {
    type: Object,
    default: () => ({
      10: { label: "单选" },
      20: { label: "多选" },
      30: { label: "填空" },
      40: { label: "判断" },
    }),
  },
});

const emit = defineEmits(["delete", "update"]);

// 创建本地响应式数据副本，避免直接修改 props
const localData = ref({ ...props.questionData });

// 监听 props 变化，同步到本地数据
watch(
  () => props.questionData,
  (newValue) => {
    localData.value = { ...newValue };
  },
  { deep: true, immediate: true }
);

// 监听本地数据变化，实时同步到父组件
watch(
  localData,
  (newValue) => {
    emit("update", newValue);
  },
  { deep: true }
);

const questionTypeLabel = computed(() => {
  return props.questionTypeMap[localData.value.q_type]?.label + ".题目";
});

// 初始化数据
onMounted(() => {
  // 确保数据结构完整
  if (!localData.value.images) {
    localData.value.images = [];
  }
  if (!localData.value.correctAnswers && localData.value.q_type === 20) {
    localData.value.correctAnswers = [];
  }
});

// 图片上传相关
const imageUploadRef = ref<HTMLInputElement>();

function triggerImageUpload() {
  imageUploadRef.value?.click();
}

function handleImageUpload(event: Event) {
  const target = event.target as HTMLInputElement;
  const files = target.files;
  if (!files) return;

  const remainingSlots = 6 - (localData.value.images?.length || 0);
  const filesToProcess = Array.from(files).slice(0, remainingSlots);

  filesToProcess.forEach((file) => {
    // 检查文件大小 (2MB)
    if (file.size > 2 * 1024 * 1024) {
      ElMessage.warning(`图片 ${file.name} 超过 2MB，已跳过`);
      return;
    }

    // 检查文件类型
    if (!file.type.startsWith("image/")) {
      ElMessage.warning(`文件 ${file.name} 不是图片格式，已跳过`);
      return;
    }

    // 创建预览URL
    const reader = new FileReader();
    reader.onload = (e) => {
      if (!localData.value.images) {
        localData.value.images = [];
      }
      localData.value.images.push({
        url: e.target?.result as string,
        file: file,
        name: file.name,
      });
    };
    reader.readAsDataURL(file);
  });

  // 清空input值，允许重复选择同一文件
  target.value = "";
  console.log("localData.value.images", localData.value.images);
}

function removeImage(index: number) {
  if (localData.value.images) {
    localData.value.images.splice(index, 1);
  }
}

// 选项操作函数
function handleSingleChoice(checked: boolean, optIndex: number) {
  if (checked) {
    localData.value.correctAnswer = String.fromCharCode(65 + optIndex);
  } else {
    localData.value.correctAnswer = "";
  }
}

function handleMultipleChoice(checked: boolean, optIndex: number) {
  const optionKey = String.fromCharCode(65 + optIndex);
  if (!localData.value.correctAnswers) {
    localData.value.correctAnswers = [];
  }

  if (checked) {
    if (!localData.value.correctAnswers.includes(optionKey)) {
      localData.value.correctAnswers.push(optionKey);
    }
  } else {
    const index = localData.value.correctAnswers.indexOf(optionKey);
    if (index > -1) {
      localData.value.correctAnswers.splice(index, 1);
    }
  }
}

function handleJudgmentChoice(checked: boolean, option: string) {
  if (checked) {
    localData.value.correctAnswer = option;
  } else {
    localData.value.correctAnswer = "";
  }
}

// 题目操作函数
function handleDelete() {
  emit("delete", props.questionIndex, localData.value);
}

// 添加选项
function addOption() {
  if (localData.value.options && localData.value.options.length < 6) {
    localData.value.options.push({ text: "" });
  }
}

// 删除选项
function removeOption(optIndex: number) {
  if (localData.value.options && localData.value.options.length > 2) {
    localData.value.options.splice(optIndex, 1);

    // 如果删除的选项是正确答案，需要清空正确答案
    const deletedOptionKey = String.fromCharCode(65 + optIndex);
    if (
      localData.value.q_type === 10 &&
      localData.value.correctAnswer === deletedOptionKey
    ) {
      localData.value.correctAnswer = "";
    } else if (
      localData.value.q_type === 20 &&
      localData.value.correctAnswers &&
      localData.value.correctAnswers.includes(deletedOptionKey)
    ) {
      const answerIndex =
        localData.value.correctAnswers.indexOf(deletedOptionKey);
      localData.value.correctAnswers.splice(answerIndex, 1);
    }
  }
}
</script>

<style scoped lang="scss">
.question-item {
  width: 100%;
  padding: 20px;
  box-shadow: inset 3px 3px 6px 1px rgba(188, 198, 214, 0.25);
  border-radius: 8px;
  border: 1px solid #edeff4;
  margin-bottom: 20px;
  background: #fff;

  &:last-child {
    margin-bottom: 0;
  }

  .label {
    width: 80px;
    min-width: 80px;
    display: flex;
    // justify-content: center;
    align-items: center;
    flex-direction: column;

    svg {
      margin-top: 10px;
      cursor: pointer;
      font-size: 28px;
      color: #00918c;
    }
  }
  .input {
    width: 100%;
  }
  .question-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding: 10px 0;
    width: 100%;

    .question-header-left {
      display: flex;
      gap: 12px;
      width: 100%;
      font-weight: 500;
      font-size: 15px;
      color: #3b4664;
    }

    .question-actions {
      padding: 0 10px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex-direction: column;
      height: 70px;

      .drag-sort {
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-size: 24px;
      }
      .delete-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-size: 24px;
      }
    }
  }

  .question-img-list {
    margin-left: 100px;
    display: flex;
    flex-wrap: wrap;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 15px;
    .img-upload {
      display: flex;
      align-items: center;
      // justify-content: center;
      font-size: 27px;
      svg {
        cursor: pointer;
      }
      .img-tips {
        margin-left: 10px;
        font-weight: 400;
        font-size: 14px;
        color: #8d9295;
        line-height: 1;
      }
    }
    .upload-list {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      .img-item {
        position: relative;
        width: 172px;
        height: 96px;
        background: #61c7bc;
        border-radius: 8px 8px 8px 8px;
        border: 1px solid #dcdfe6;
        display: flex;
        align-items: center;
        justify-content: center;

        img {
          width: 162px;
          height: 86px;
          object-fit: cover;
        }

        .img-delete {
          position: absolute;
          top: -12px;
          right: -12px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          font-size: 19px;
          &:hover {
            transform: scale(1.2);
          }
        }
      }
    }
  }

  .question-form {
    .green-block {
      min-height: 116px;
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 24px;
      background: linear-gradient(180deg, #eaf8e5 0%, #e2f8e2 100%);
      box-shadow: 0px 0px 6px 1px rgba(191, 226, 206, 0.16);
      border-radius: 13px 13px 13px 13px;
      border: 1px solid #ffffff;
      :deep(.el-textarea__inner),
      :deep(.el-input__inner) {
        background: #ffffff !important;
      }
    }
  }
  .question-analysis {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
    padding: 10px 0;
    width: 100%;
    padding-right: 20px;
    gap: 12px;
  }
  .question-options {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
    padding: 10px 0;
    width: 100%;
    padding-right: 20px;
    gap: 12px;
    .options-container {
      display: flex;
      flex-wrap: wrap;
      width: 100%;
      gap: 10px;
      background: #ffffff;
      box-shadow: inset 3px 3px 6px 1px rgba(188, 198, 214, 0.14);
      border-radius: 8px 8px 8px 8px;
      border: 1px solid #edeff4;
      :deep(.el-textarea__inner),
      :deep(.el-input__wrapper) {
        border-radius: none !important;
        box-shadow: none !important;
        background-color: none !important;
        border: none !important;
      }
      .option-item {
        width: 100%;
        border-bottom: 1px solid #c1c7d5;
        display: flex;
        align-items: center;
        padding: 10px 10px;
        .option-label {
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 0 10px;
          // width: 60px;
          svg {
            margin-right: 10px;
            font-size: 28px;
          }
          .option-letter {
            margin-left: 10px;
            font-weight: 500;
            color: #333;
          }
        }
        .delete-icon {
          margin-left: 10px;
          cursor: pointer;
        }
        &:last-child {
          border-bottom: none;
        }
      }
    }
  }
}
</style>
