div
<template>
  <div class="question-bank-container">
    <div class="left-sidebar">
      <!-- 题库主题列表 -->
      <el-scrollbar
        style="width: 100%; height: 90%"
        warp-style="overflow-x: hidden;"
      >
        <div class="sidebar-list">
          <draggable
            v-model="sidebarTopics"
            class="drag-container"
            :item-key="'topic'"
            @end="endVideosMove"
            :handle="'.sort'"
          >
            <template #item="{ element: item, index: idx }">
              <div
                class="green-item"
                :key="idx"
                @click="handleTopicClick('active', item)"
                :class="[item.id === activeTopic ? 'active-item' : '']"
              >
                <div class="green-title">
                  <div class="left">
                    <div class="icon sort">
                      <svg-icon icon-class="drag3" v-if="idx !== 0" />
                    </div>
                    <span class="text">{{ item.name }} </span>
                  </div>

                  <div class="right" v-if="idx !== 0">
                    <div
                      class="icon edit"
                      @click="handleTopicClick('edit', item)"
                    >
                      <svg-icon icon-class="edit2" />
                    </div>
                    <div
                      class="icon delete"
                      @click="handleTopicClick('delete', item)"
                    >
                      <svg-icon icon-class="delete" />
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </draggable>
        </div>
      </el-scrollbar>

      <div
        class="sidebar-footer"
        style="position: absolute; bottom: 20px; width: 90%"
      >
        <!-- <div class="btn text-btn" @click="handleTopicClick('edit')">编辑</div> -->
        <div class="btn text-btn" @click="handleTopicClick('create')">
          新增主题
        </div>
        <!-- <div class="btn text-btn" @click="handleTopicClick('save')">保存</div> -->
      </div>
    </div>
    <div class="right-content">
      <div class="container-header">
        <div class="left">
          <div class="filter-row">
            <el-input
              v-model="queryParams.search"
              placeholder="请输入关键字"
              clearable
              size="large"
            />
          </div>
          <div class="btn primary-btn" @click="handleQuery">
            <i-ep-search /> 搜索
          </div>
        </div>
        <div class="right">
          <div class="text-btn" @click="handleCreate">
            <i-ep-circle-plus style="margin-right: 5px" />
            新增题库
          </div>
        </div>
      </div>

      <div class="content">
        <el-table
          v-loading="loading"
          element-loading-text="Loading"
          element-loading-background="#ffffffb4"
          :data="tableData"
          height="100%"
          border
          fit
          highlight-current-row
        >
          <el-table-column label="题库名称" align="center" min-width="120">
            <template #default="scope">
              {{ scope.row.bankName }}
            </template>
          </el-table-column>
          <el-table-column label="关联课程" align="center" min-width="100">
            <template #default="scope">
              {{ getQuestionTypeText(scope.row.questionType) }}
            </template>
          </el-table-column>
          <el-table-column label="题目数量" align="center" min-width="60">
            <template #default="scope">
              <span style="color: #00918c"> {{ scope.row.questionType }}</span>
            </template>
          </el-table-column>
          <!-- <el-table-column label="试题类型" align="center" min-width="100">
          <template #default="scope">
            {{ getQuestionTypeText(scope.row.questionType) }}
          </template>
        </el-table-column> -->

          <el-table-column label="创建人" align="center" min-width="60">
            <template #default="scope">
              {{ scope.row.creator }}
            </template>
          </el-table-column>

          <el-table-column label="创建时间" align="center" min-width="120">
            <template #default="scope">
              {{ scope.row.createdAt }}
            </template>
          </el-table-column>

          <el-table-column label="状态" align="center" min-width="80">
            <template #default="scope">
              <el-tag :type="scope.row.status === 10 ? 'success' : 'danger'">
                {{ scope.row.status === 10 ? "启用" : "停用" }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="操作" align="center" min-width="240">
            <template #default="scope">
              <div class="option-btn">
                <div
                  class="btn deep-blue-btn"
                  @click="onRowClick('link', scope.row)"
                >
                  关联课程
                </div>
                <div
                  class="btn"
                  :class="[
                    scope.row.status == 10 || scope.row.status == 30
                      ? 'light-green-btn'
                      : 'info-btn',
                  ]"
                  @click="onRowClick('status', scope.row)"
                >
                  {{
                    scope.row.status == 10 || scope.row.status == 30
                      ? "启用"
                      : scope.row.status == 20
                      ? "停用"
                      : "--"
                  }}
                </div>
                <div
                  class="btn light-blue-btn"
                  @click="onRowClick('detail', scope.row)"
                >
                  详情
                </div>
                <div
                  class="btn primary-btn"
                  @click="onRowClick('edit', scope.row)"
                >
                  修改
                </div>
                <div
                  class="btn delete-btn"
                  @click="onRowClick('delete', scope.row)"
                >
                  删除
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="footer">
        <pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getData"
        />
      </div>
    </div>

    <!-- 关联课程弹窗 -->
    <el-dialog
      class="link-course-dialog"
      v-model="linkCourseDialog.visible"
      :title="linkCourseDialog.title"
      :width="linkCourseDialog.width"
      :before-close="closeLinkCourseDialog"
      append-to-body
    >
      <div class="link-course-content">
        <div
          class="course-selection custom-checkbox-rectangle checkbox-rectangle-green"
        >
          <!-- 左侧：未关联课程 -->
          <div class="course-list">
            <div class="list-header unlinked">
              <span class="header-title">未关联课程</span>
            </div>
            <div class="search-box">
              <div class="select-all-box">
                <el-checkbox
                  :model-value="isAllUnlinkedSelected"
                  :indeterminate="isUnlinkedIndeterminate"
                  @change="handleSelectAllUnlinked"
                >
                  全选
                </el-checkbox>
              </div>
              <el-input
                v-model="linkCourseForm.leftSearch"
                placeholder="请输入名称"
                clearable
                size="large"
              >
                <template #suffix>
                  <el-icon><i-ep-search /></el-icon>
                </template>
              </el-input>
            </div>
            <div class="course-items">
              <div
                v-for="course in filteredUnlinkedCourses"
                :key="course.id"
                class="course-item"
                @click="toggleCourseSelection(course, 'unlinked')"
              >
                <el-checkbox
                  :model-value="
                    linkCourseForm.selectedUnlinkedIds.includes(course.id)
                  "
                  @change="
                    (checked) =>
                      handleCheckboxChange(course, 'unlinked', checked)
                  "
                  @click.stop
                >
                  {{ course.name }}
                </el-checkbox>
              </div>
            </div>
          </div>

          <!-- 中间：操作按钮 -->
          <div class="operation-buttons">
            <div class="btn-group">
              <div
                class="right-btn"
                @click="addCourseLink"
                :class="{
                  'disabled-btn':
                    linkCourseForm.selectedUnlinkedIds.length === 0,
                }"
              >
                <!-- → -->
              </div>

              <div
                class="left-btn"
                @click="removeCourseLink"
                :class="{
                  'disabled-btn': linkCourseForm.selectedLinkedIds.length === 0,
                }"
              >
                <!-- ← -->
              </div>
            </div>
          </div>

          <!-- 右侧：已关联课程 -->
          <div class="course-list">
            <div class="list-header linked">
              <span class="header-title">已关联课程</span>
            </div>
            <div class="search-box">
              <div class="select-all-box">
                <el-checkbox
                  :model-value="isAllLinkedSelected"
                  :indeterminate="isLinkedIndeterminate"
                  @change="handleSelectAllLinked"
                >
                  全选
                </el-checkbox>
              </div>
              <el-input
                v-model="linkCourseForm.rightSearch"
                placeholder="请输入名称"
                clearable
                size="large"
              >
                <template #suffix>
                  <el-icon><i-ep-search /></el-icon>
                </template>
              </el-input>
            </div>
            <div class="course-items">
              <div
                v-for="course in filteredLinkedCourses"
                :key="course.id"
                class="course-item"
                @click="toggleCourseSelection(course, 'linked')"
              >
                <el-checkbox
                  :model-value="
                    linkCourseForm.selectedLinkedIds.includes(course.id)
                  "
                  @change="
                    (checked) => handleCheckboxChange(course, 'linked', checked)
                  "
                  @click.stop
                >
                  {{ course.name }}
                </el-checkbox>
              </div>
            </div>
          </div>
        </div>

        <!-- 底部提示 -->
        <div class="tips">
          <span class="tip-text"
            >注：一个题库可关联多个课程；一个课程只能关联一个题库</span
          >
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <div class="btn cancel-btn" @click="closeLinkCourseDialog">取 消</div>
          <div class="btn primary-btn" @click="saveLinkCourse">保 存</div>
        </div>
      </template>
    </el-dialog>

    <!-- 主题弹窗 -->
    <el-dialog
      v-model="topicDialog.visible"
      :title="topicDialog.title"
      :width="topicDialog.width"
      :before-close="closeTopicDialog"
      append-to-body
    >
      <div class="dialog-body">
        <el-form :model="topicForm" ref="topicFormRef">
          <el-form-item
            label="主题名称"
            prop="name"
            :rules="[
              { required: true, message: '请输入主题名称', trigger: 'blur' },
            ]"
          >
            <el-input
              v-model="topicForm.name"
              placeholder="请输入主题名称"
              maxlength="10"
              show-word-limit
              size="large"
              clearable
            />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <div class="btn cancel-btn" @click="closeTopicDialog">取 消</div>
          <div class="btn primary-btn" @click="saveTopic">保 存</div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import draggable from "vuedraggable";
import { useUserStore } from "@/store/modules/user";

import { useRoute, useRouter } from "vue-router";

import {
  parseTime,
  resetReactiveObject,
  secondsToHoursAndMinutes,
  numberToChinese,
  numberToChinese2,
} from "@/utils";
import { checkUserPermission } from "@/utils/auth";
import {
  getTopics,
  addTopic,
  updateTopic,
  deleteTopic,
  getQuestionBanks,
  addQuestionBank,
  updateQuestionBank,
  deleteQuestionBank,
} from "@/api/exam";
import { getCourses } from "@/api/course";
const { proxy } = getCurrentInstance() as any;

defineOptions({
  name: "QuestionBank",
  inheritAttrs: false,
});

const route = useRoute();
const router = useRouter();
const loading = ref<any>(false);
const total = ref<any>(0);

const queryParams = reactive<any>({
  search: "",
  pageNum: 1,
  pageSize: 20,
});

const activeTopic = ref<any>(0);
const sidebarTopics = ref<any>([
  {
    id: 0,
    name: "全部",
  },
  {
    id: 1,
    name: "基础知识",
  },
  {
    id: 2,
    name: "节能管理",
  },
]);
const topicId = ref<any>(null);

const tableData = ref<any>([
  {
    id: 1,
    bankName: "安全生产知识题库",
    questionType: 1, // 1-单选题, 2-多选题, 3-判断题, 4-填空题, 5-简答题
    creator: "管理员",
    createdAt: "2024-03-20 10:13:21",
    status: 10, // 1-启用, 0-禁用
  },
  {
    id: 2,
    bankName: "消防安全题库",
    questionType: 2,
    creator: "张三",
    createdAt: "2024-03-19 15:30:45",
    status: 10,
  },
  {
    id: 3,
    bankName: "职业健康题库",
    questionType: 3,
    creator: "李四",
    createdAt: "2024-03-18 09:25:30",
    status: 10,
  },
  {
    id: 4,
    bankName: "环境保护题库",
    questionType: 4,
    creator: "王五",
    createdAt: "2024-03-17 14:18:12",
    status: 20,
  },
  {
    id: 5,
    bankName: "应急管理题库",
    questionType: 10,
    creator: "赵六",
    createdAt: "2024-03-16 11:42:55",
    status: 10,
  },
]);

// 10-单选题，20-多选题，30-填空题，40-判断题
const questionTypeOptions = ref<any>([
  { label: "单选题", value: 10 },
  { label: "多选题", value: 20 },
  { label: "判断题", value: 30 },
  { label: "填空题", value: 40 },
]);
// 题目类型映射
const questionTypeMap = reactive<any>({
  10: "单选题",
  20: "多选题",
  30: "判断题",
  40: "填空题",
});

// 关联课程弹窗数据
const linkCourseDialog = reactive<any>({
  visible: false,
  width: "50%",
  title: "关联课程",
});
const linkCourseForm = reactive<any>({
  currentQuestionBank: null as any,
  leftSearch: "",
  rightSearch: "",
  selectedUnlinkedIds: [] as number[], //  多选
  selectedLinkedIds: [] as number[], //   多选
});

// 模拟课程数据
const allCourses = ref<any>([
  { id: 1, name: "安全生产基础知识", linked: false },
  { id: 2, name: "消防安全管理", linked: true },
  { id: 3, name: "职业健康防护", linked: false },
  { id: 4, name: "环境保护法规", linked: false },
  { id: 5, name: "应急救援技能", linked: true },
  { id: 6, name: "危险化学品管理", linked: false },
  { id: 7, name: "特种设备安全", linked: false },
  { id: 8, name: "建筑施工安全", linked: false },
]);

// 过滤未关联课程
const filteredUnlinkedCourses = computed(() => {
  return allCourses.value
    .filter((course) => !course.linked)
    .filter((course) =>
      course.name
        .toLowerCase()
        .includes(linkCourseForm.leftSearch.toLowerCase())
    );
});

//  过滤已关联课程
const filteredLinkedCourses = computed(() => {
  return allCourses.value
    .filter((course) => course.linked)
    .filter((course) =>
      course.name
        .toLowerCase()
        .includes(linkCourseForm.rightSearch.toLowerCase())
    );
});

// 未关联课程全选状态
const isAllUnlinkedSelected = computed(() => {
  const filtered = filteredUnlinkedCourses.value;
  return (
    filtered.length > 0 &&
    filtered.every((course) =>
      linkCourseForm.selectedUnlinkedIds.includes(course.id)
    )
  );
});

// 未关联课程半选状态
const isUnlinkedIndeterminate = computed(() => {
  const filtered = filteredUnlinkedCourses.value;
  const selectedCount = filtered.filter((course) =>
    linkCourseForm.selectedUnlinkedIds.includes(course.id)
  ).length;
  return selectedCount > 0 && selectedCount < filtered.length;
});

// 已关联课程全选状态
const isAllLinkedSelected = computed(() => {
  const filtered = filteredLinkedCourses.value;
  return (
    filtered.length > 0 &&
    filtered.every((course) =>
      linkCourseForm.selectedLinkedIds.includes(course.id)
    )
  );
});

// 已关联课程半选状态
const isLinkedIndeterminate = computed(() => {
  const filtered = filteredLinkedCourses.value;
  const selectedCount = filtered.filter((course) =>
    linkCourseForm.selectedLinkedIds.includes(course.id)
  ).length;
  return selectedCount > 0 && selectedCount < filtered.length;
});

//主题相关
const topicDialog = reactive<any>({
  visible: false,
  title: "新增主题",
  width: "25%",
});
const topicForm = reactive<any>({
  name: "",
});
const topicFormRef = ref<any>();

onBeforeMount(() => {});

onMounted(() => {
  getData();
  getCoursesData();
});

function getQuestionTypeText(type: any) {
  return questionTypeMap[type] || "未知";
}

function handleQuery() {
  queryParams.pageNum = 1;
  getData();
}

function handleCreate() {
  router.push({
    path: "question-bank-action",
    query: { type: "create" },
  });
}
function getCoursesData() {
  const params = {
    page: 1,
    per_page: 9999,
  };
  getCourses(params).then((res: any) => {
    allCourses.value = res.data.courses.map((item: any) => {
      item.name = item.title;
      return item;
    });
  });
}
function getData() {
  loading.value = true;

  const params = {
    search: queryParams.search || undefined,
    page: queryParams.pageNum,
    per_page: queryParams.pageSize,
    topic_id: topicId.value === 0 ? undefined : topicId.value,
  };
  setTimeout(() => {
    // getQuestionBanks(params).then((res: any) => {
    //   tableData.value = res.data.questions.map((item: any) => {
    //     item.created_at = parseTime(item.created_at, "{y}-{m}-{d} {h}:{i}:{s}");
    //     return item;
    //   });
    //   total.value = res.total;
    //   loading.value = false;
    // });
    total.value = tableData.value.length;
    loading.value = false;
  }, 500);
}
function onRowClick(type: string, row: any) {
  switch (type) {
    case "link":
      console.log("关联课程", row);
      openLinkCourseDialog(row);
      break;
    case "status":
      const status =
        row.status == 10 || row.status == 30 ? 20 : row.status == 20 ? 30 : "";
      const message =
        row.status == 10 || row.status == 30
          ? "启用成功"
          : row.status == 20
          ? "停用成功"
          : "";
      const text = row.status == 20 ? "停用" : "启用";

      ElMessageBox.confirm("此操作将" + text + "该题库，是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        //
      });
      break;

    case "detail":
      router.push({
        path: "question-bank-detail",
        query: { id: row.id, type: "detail" },
      });
      break;
    case "edit":
      router.push({
        path: "question-bank-action",
        query: { id: row.id, type: "edit" },
      });
      break;
    case "delete":
      handleDelete(row);
      break;
  }
}

function handleDelete(row: any) {
  ElMessageBox.confirm(`确定要删除题库"${row.bankName}"吗？`, "删除确认", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      console.log("删除", row);
      ElMessage.success("删除成功");
      getData();
    })
    .catch(() => {
      ElMessage.info("已取消删除");
    });
}

// 关联课程相关方法
function openLinkCourseDialog(row: any) {
  linkCourseForm.currentCourseData = row;
  linkCourseDialog.visible = true;
  linkCourseForm.leftSearch = "";
  linkCourseForm.rightSearch = "";
  linkCourseForm.selectedUnlinkedIds = [];
  linkCourseForm.selectedLinkedIds = [];
}

// 处理checkbox变化事件
function handleCheckboxChange(course: any, type: string, checked: boolean) {
  if (type === "unlinked") {
    if (checked) {
      // 添加
      if (!linkCourseForm.selectedUnlinkedIds.includes(course.id)) {
        linkCourseForm.selectedUnlinkedIds.push(course.id);
      }
    } else {
      //  移除
      const index = linkCourseForm.selectedUnlinkedIds.indexOf(course.id);
      if (index > -1) {
        linkCourseForm.selectedUnlinkedIds.splice(index, 1);
      }
    }
  } else {
    if (checked) {
      // 添加
      if (!linkCourseForm.selectedLinkedIds.includes(course.id)) {
        linkCourseForm.selectedLinkedIds.push(course.id);
      }
    } else {
      //  移除
      const index = linkCourseForm.selectedLinkedIds.indexOf(course.id);
      if (index > -1) {
        linkCourseForm.selectedLinkedIds.splice(index, 1);
      }
    }
  }
}

// 点击课程项切换选择状态
function toggleCourseSelection(course: any, type: string) {
  if (type === "unlinked") {
    const isSelected = linkCourseForm.selectedUnlinkedIds.includes(course.id);
    handleCheckboxChange(course, type, !isSelected);
  } else {
    const isSelected = linkCourseForm.selectedLinkedIds.includes(course.id);
    handleCheckboxChange(course, type, !isSelected);
  }
}

// 处理未关联课程全选
function handleSelectAllUnlinked(checked: boolean) {
  if (checked) {
    const allUnlinkedIds = filteredUnlinkedCourses.value.map(
      (course) => course.id
    );
    linkCourseForm.selectedUnlinkedIds = [...allUnlinkedIds];
  } else {
    linkCourseForm.selectedUnlinkedIds = [];
  }
}

// 处理已关联课程全选
function handleSelectAllLinked(checked: boolean) {
  if (checked) {
    const allLinkedIds = filteredLinkedCourses.value.map((course) => course.id);
    linkCourseForm.selectedLinkedIds = [...allLinkedIds];
  } else {
    linkCourseForm.selectedLinkedIds = [];
  }
}

function addCourseLink() {
  if (linkCourseForm.selectedUnlinkedIds.length > 0) {
    let successCount = 0;
    linkCourseForm.selectedUnlinkedIds.forEach((courseId) => {
      const course = allCourses.value.find((c) => c.id === courseId);
      if (course) {
        course.linked = true;
        successCount++;
      }
    });

    linkCourseForm.selectedUnlinkedIds = [];
  }
}

function removeCourseLink() {
  if (linkCourseForm.selectedLinkedIds.length > 0) {
    let successCount = 0;
    linkCourseForm.selectedLinkedIds.forEach((courseId) => {
      const course = allCourses.value.find((c) => c.id === courseId);
      if (course) {
        course.linked = false;
        successCount++;
      }
    });

    linkCourseForm.selectedLinkedIds = [];
  }
}

function closeLinkCourseDialog() {
  linkCourseDialog.visible = false;
  linkCourseForm.currentCourseData = null;
  linkCourseForm.selectedUnlinkedIds = [];
  linkCourseForm.selectedLinkedIds = [];
}

function saveLinkCourse() {
  console.log("保存关联课程", linkCourseForm.currentCourseData);
  ElMessage.success("保存成功");
  closeLinkCourseDialog();
}

function endVideosMove() {
  //  批量更新排序
  // const updatePromises = sidebarTopics.value
  //   .filter((item) => item.id !== 0) // 跳过“全部”或不需要排序的项
  //   .map((item, index) => updateTopic(item.id, { seq: index }));
  // Promise.all(updatePromises)
  //   .then(() => {
  //     ElMessage.success("排序成功");
  //   })
  //   .catch(() => {
  //     ElMessage.error("排序失败，请重试");
  //   });
}

// 左侧主题操作
function getTopicsData() {
  getTopics().then((res: any) => {
    sidebarTopics.value = res.data;
  });
}

function handleTopicClick(type?: any, row?: any) {
  if (type === "active") {
    topicId.value = row?.id;
    activeTopic.value = row?.id;
  }
  if (type === "create") {
    topicDialog.visible = true;
    topicDialog.title = "新增主题";
  }
  if (type === "edit") {
    console.log("编辑", row);
    topicId.value = row.id;
    topicDialog.title = "编辑主题";
    topicDialog.visible = true;
    topicForm.name = row?.name || "";
    topicForm.id = row?.id || undefined;
  }
  if (type === "delete") {
    console.log("删除", row);
    handleDeleteTopic(row);
  }
}
function closeTopicDialog() {
  topicDialog.visible = false;
  topicForm.name = "";
  topicForm.id = undefined;
  (topicFormRef.value as any).resetFields();
}
function saveTopic() {
  (topicFormRef.value as any).validate((valid: boolean) => {
    if (!valid) return;
    if (topicForm.id) {
      updateTopic(topicForm.id, { name: topicForm.name }).then(() => {
        ElMessage.success("编辑成功");
        closeTopicDialog();
        getTopicsData();
      });
    } else {
      addTopic({ name: topicForm.name }).then(() => {
        ElMessage.success("新增成功");
        closeTopicDialog();
        getTopicsData();
      });
    }
  });
}

function handleDeleteTopic(row?: any) {
  ElMessageBox.confirm(`确定要删除${row.name} 主题吗？`, "删除确认", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    deleteTopic(topicId.value).then(() => {
      ElMessage.success("删除成功");
      handleTopicClick("active", { id: 0 });
      getTopicsData();
    });
  });
}
</script>

<style scoped lang="scss">
.question-bank-container {
  position: relative;
  // background: #fff;
  // border-radius: 8px;
  // box-shadow: 0 3px 20px 1px rgb(109 125 126 / 7%);
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 95%;
  margin: 20px;

  .left-sidebar {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 15%;
    height: 100%;
    padding: 0;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 3px 20px 1px rgb(109 125 126 / 7%);

    .sidebar-list {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 100%;

      .drag-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 100%;

        .green-item {
          display: flex;
          align-items: center;
          width: 90%;
          min-height: 60px;
          // height: 60px;
          padding: 20px 0;
          font-size: 16px;
          font-weight: 500;
          color: #3b4664;
          cursor: pointer;
          border-bottom: 1px solid #c1c7d5;
          // background: linear-gradient(180deg, #eaf8e5 0%, #e2f8e2 100%);
          // border: 1px solid #fff;
          //  box-shadow: 0 0 6px 1px rgb(191 226 206 / 16%);
          // border-radius: 13px;

          &:hover {
            color: #fff;
            background: linear-gradient(180deg, #00918c 0%, #1c8d84 100%);
            transform: scale(1.1);
          }

          .green-title {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            padding: 0 10px;

            .icon {
              display: flex;
              align-items: center;
              justify-content: center;
              margin-right: 10px;
              cursor: pointer;
            }

            .left {
              display: flex;
              align-items: center;
              justify-content: center;
              width: 80%;

              .text {
                width: 80%;
                // overflow: hidden;
                // text-overflow: ellipsis;
                // white-space: nowrap;
              }
            }

            .right {
              display: flex;
              align-items: center;
              width: 20%;

              span {
                display: inline-block;
              }
            }
          }
        }

        .green-item:first-child {
          height: 44px;
          min-height: 44px;
          padding: 10px 0;
          border-radius: 8px 8px 0 0;

          .green-title,
          .left,
          .text {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100% !important;
          }

          .icon {
            margin: 0 !important;
          }
        }

        .green-item:last-child {
          // border-bottom: transparent;
        }

        .active-item {
          color: #fff !important;
          background: linear-gradient(180deg, #00918c 0%, #1c8d84 100%);
          transform: scale(1.1);
        }
      }

      .sidebar-item {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 10px 0;
        cursor: pointer;
      }
    }

    .sidebar-footer {
      display: flex;
      align-items: center;
      justify-content: space-around;
      margin-top: 15px;
      // position: relative;
    }
  }

  .right-content {
    width: 84%;
    height: 100%;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 3px 20px 1px rgb(109 125 126 / 7%);
  }

  .pre-header {
    width: 100%;
    padding: 20px 20px 0;
  }

  .container-header {
    display: flex;
    justify-content: space-between;
    width: 100%;
    padding: 20px;

    .left {
      display: flex;
      align-items: center;
      justify-content: space-between;
      // width: 60%;
      .btn {
        width: 116px;
        height: 40px;
        margin-left: 20px;
        font-size: 17px;
      }
    }

    .right {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .filter-row {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 260px;
      margin-left: 20px;

      &:nth-child(1) {
        width: 260px;
        margin-left: 0;
      }

      .btn {
        width: 116px;
        height: 40px;
        margin-left: 20px;
      }
    }
  }

  .content {
    width: 100%;
    height: calc(100% - 170px);
    padding: 10px 20px;
  }

  .footer {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 15px;
  }
}

// 关联课程弹窗样式
.link-course-dialog {
  .link-course-content {
    width: 100%;

    .course-selection {
      display: flex;
      gap: 20px;
      height: 50vh;

      .course-list {
        display: flex;
        flex-direction: column;
        width: calc(50% - 40px);
        border: 1px solid #e4e7ed;
        border-radius: 12px 12px 0 0;

        .list-header {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 68px;
          padding: 10px;
          font-weight: 500;
          color: white;
          text-align: center;
          border-radius: 12px 12px 0 0;

          .header-title {
            font-size: 18px;
            font-weight: 500;
          }
        }

        .unlinked {
          background: url("@/assets/exam/grey-header.png") no-repeat;
          background-size: 100% 100%;
        }

        .linked {
          background: url("@/assets/exam/green-header.png") no-repeat;
          background-size: 100% 100%;
        }

        .search-box {
          display: flex;
          align-items: center;
          padding: 10px;
          border-bottom: 1px solid #e4e7ed;

          .select-all-box {
            padding: 0 20px 0 10px;
          }
        }

        .course-items {
          flex: 1;
          width: 100%;
          padding: 10px;
          overflow-y: auto;

          .course-item {
            width: 100%;
            padding: 8px;
            cursor: pointer;

            .el-checkbox {
              max-width: 90%;
              line-height: 1.5;
              word-break: break-all;
              white-space: normal;
            }
          }
        }
      }

      .operation-buttons {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 80px;

        .btn-group {
          display: flex;
          flex-direction: column;
          gap: 10px;
          .right-btn {
            cursor: pointer;
            width: 40px;
            height: 40px;
            background: url("@/assets/images/move-right.png") no-repeat;
            background-size: 100% 100%;
          }
          .left-btn {
            cursor: pointer;
            width: 40px;
            height: 40px;
            background: url("@/assets/images/move-left.png") no-repeat;
            background-size: 100% 100%;
          }
        }
      }
    }

    .tips {
      padding: 10px;
      margin-top: 20px;
      background-color: #fff6f7;
      border: 1px solid #fbc4c4;
      border-radius: 4px;

      .tip-text {
        font-size: 12px;
        color: #e6a23c;
      }
    }
  }
}
</style>
