<template>
  <!-- 判断题组件，只有A、B对错 -->
  <div
    class="judgement-container"
    ref="judgementContainer"
    :style="{
      ...getContainerStyle(),
    }"
  >
    <div class="question-panel">
      <div class="question-title">
        <div
          v-if="showCheckBox"
          class="left-controls custom-checkbox-rectangle checkbox-rectangle-green"
        >
          <el-checkbox v-model="selectedQuestion" @change="handleSelect" />
        </div>

        <span class="text"
          >{{ questionIndex + 1 }}.{{ questionData.content.title }}
        </span>
        <span class="score-tip">
          （判断题
          <span v-if="showQuestionScore">·{{ questionData.score }}分</span>
          ）
        </span>
      </div>
      <div
        class="answer-row"
        :style="{
          'pointer-events': isOperable ? 'auto' : 'none',
        }"
      >
        <div
          class="answer-item custom-checkbox-circle checkbox-circle-green2"
          v-for="(item, index) in answerOptions"
          :key="index"
        >
          <el-checkbox
            :model-value="
              (showSelectedAnswer ? questionData.answers : answersData) ===
              item.key
            "
            @change="() => changeAnswer(item.key)"
            :label="item.key"
            :checked="
              (showSelectedAnswer ? questionData.answers : answersData) ===
              item.key
            "
            :true-label="item.key"
            :false-label="''"
            class="single-checkbox"
          >
            {{ item.key }} . {{ item.label }}
          </el-checkbox>
          <div v-if="item.img" class="option-img">
            <img :src="item.img" alt="选项图片" />
          </div>
        </div>
      </div>
      <div class="result-row" v-if="showAnswer">
        <div
          class="result-answer"
          :class="{
            correct: isCorrect && showGetScore,
            wrong: !isCorrect && showGetScore,
          }"
        >
          <span class="highlight"
            >答案： {{ questionData.content.answers }} </span
          >；
          <span class="score" v-if="showGetScore"> 得分{{ score }}</span>
        </div>
        <div
          class="result-analysis"
          v-if="showAnalysis && questionData.content.remark"
        >
          <div class="analysis-title">
            解析:
            <span>{{ questionData.content.remark }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useAppStore } from "@/store/modules/app";
import { useRoute, useRouter } from "vue-router";
//const { proxy } = getCurrentInstance();
// proxy 替代 this
defineOptions({
  name: "Judgement",
  inheritAttrs: false,
});
const props = defineProps({
  isOperable: {
    type: Boolean,
    default: false,
  }, //是否只读或答题
  showQuestionScore: {
    type: Boolean,
    default: false,
  }, //是否显示题目分数
  showAnswer: {
    type: Boolean,
    default: false,
  }, //是否显示答案
  showSelectedAnswer: {
    type: Boolean,
    default: false,
  }, //是否显示选中答案
  showGetScore: {
    type: Boolean,
    default: false,
  }, //是否显示得分
  showAnswerTime: {
    type: Boolean,
    default: false,
  }, //是否显示答题时间
  showCheckBox: {
    type: Boolean,
    default: false,
  }, //是否显示题目选中的选择框

  showAnalysis: {
    type: Boolean,
    default: false,
  }, //是否显示解析
  showAnswerType: {
    type: String,
    default: "all", // all-全部, correct-正确, incorrect-错误
  }, //筛选题目答完后答题类型
  itemStyle: {
    type: String,
    default: "bottomBorder", // shadowBorder-阴影边框, bottomBorder-底部边框，其它...
  }, //试题样式
  paperData: {
    type: Object,
    default: () => {},
  }, //试卷数据
  questionData: {
    type: Object,
    default: () => {
      return {};
    },
  }, //试题数据
  questionIndex: {
    type: Number,
    default: 0,
  },
  selectedQuestionIds: {
    type: Array,
    default: () => [],
  }, //外部传入的选中题目ID列表
});
const emit = defineEmits([
  "updateAnswer",
  "updateScore",
  "submit",
  "handleAction",
]);
const store = useAppStore();
const route = useRoute();
const router = useRouter();

const questionData = computed(() => props.questionData);
const questionIndex = computed(() => props.questionIndex);
const isOperable = computed(() => props.isOperable);
const showAnswer = computed(() => props.showAnswer);
const showSelectedAnswer = computed(() => props.showSelectedAnswer);
const showGetScore = computed(() => props.showGetScore);
const showQuestionScore = computed(() => props.showQuestionScore);
const showAnswerTime = computed(() => props.showAnswerTime);
const showAnalysis = computed(() => props.showAnalysis);
const showAnswerType = computed(() => props.showAnswerType);
const itemStyle = computed(() => props.itemStyle);
const showCheckBox = computed(() => props.showCheckBox);

const answersData = ref("");
const selectedQuestion = ref<any>(false);

// 监听外部选中状态变化
watch(
  () => props.selectedQuestionIds,
  (newIds) => {
    const isSelected = newIds.includes(questionData.value.content.id);
    selectedQuestion.value = isSelected;
  },
  { deep: true, immediate: true }
);
const isCorrect = computed(() => {
  return questionData.value.answers === questionData.value.content.answers;
}); //判断是否正确
const score = computed(() => {
  return isCorrect.value ? questionData.value.score : 0;
}); //判断得分

//单选、多选、判断题选项数组
// 题库选项格式如 {A: "内容"} 或 {A: {text: "内容", img: "图片"}} 或 {A: "内容", thumb: "图片" }
const answerOptions = computed(() => {
  return questionData.value.content.options.map((item: any) => {
    const key = Object.keys(item)[0];
    const value = item[key];
    let label = "";
    let img = "";
    if (item.thumb) {
      const key2: any = Object.entries(item)[1];

      img = key2[1];
    }
    // 支持三种格式
    if (typeof value === "object" && value !== null) {
      label = value.text || value.label || "";
      // img = value.img || value.thumb || "";
    } else {
      label = value;
    }
    return { key, label, img, value: key };
  });
});
onBeforeMount(() => {});
onMounted(() => {});

function changeAnswer(answer: string) {
  answersData.value = answer;
  emit("updateAnswer", answer);
}

function getContainerStyle() {
  let style = {};
  if (itemStyle.value == "bottomBorder") {
    style = {
      " border-bottom": "1px solid hsl(200deg 6% 42% / 44%)",
    };
  }
  if (itemStyle.value == "shadowBorder") {
    style = {
      "box-shadow":
        "0px 0px 6px 1px rgba(191,226,206,0.08), inset 0px 0px 10px 1px rgba(19,69,65,0.22)",
      "border-radius": "6px 6px 6px 6px",
      border: "1px solid #FFFFFF",
      "margin-bottom": "0.8rem",
    };
  }
  return style;
}
function handleSelect() {
  // selectedQuestion.value = !selectedQuestion.value;
  emit("handleAction", {
    type: selectedQuestion.value ? "select-question" : "remove-question",
    question: questionData.value,
  });
}
</script>

<style scoped lang="scss">
.judgement-container {
  width: 100%;
  padding: 18px 18px 24px;
  border-bottom: 1px solid hsl(200deg 6% 42% / 44%);

  .question-panel {
    .question-title {
      display: flex;
      align-items: center;
      margin-bottom: 10px;
      font-size: 18px;
      font-weight: 500;
      color: #3b4664;
      .left-controls {
        margin-right: 10px;
      }
      .score-tip {
        font-size: 15px;
        font-weight: 500;
        color: #00918c;
      }
    }

    .answer-row {
      margin: 12px 0 0;
      font-size: 15px;
      color: #666;

      .answer-item {
        margin-bottom: 10px;
      }

      .answer-item:last-child {
        margin-bottom: 0;
      }

      .single-checkbox {
        display: flex;
        align-items: center;

        :deep(.el-checkbox__label) {
          margin-left: 16px !important;
          word-break: break-all;
          white-space: pre-line;
        }
      }

      .option-img {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 222px;
        height: 122px;
        margin-top: 2px;
        margin-left: 88px;
        background: #fff;
        border: 1px solid #fff;
        box-shadow: 0 0 10px 1px rgb(140 177 156 / 42%);

        img {
          width: 212px;
          height: 112px;
          object-fit: cover;
        }
      }
    }

    .result-row {
      .result-answer {
        margin: 12px 0;
        font-size: 20px;
        font-weight: 500;
        color: #00918c;
      }

      .correct {
        color: #00918c;
      }

      .wrong {
        color: #f06e15;
      }
    }

    .result-analysis {
      font-size: 20px;
      font-weight: 500;
      color: #00918c;
    }
  }
}
</style>
