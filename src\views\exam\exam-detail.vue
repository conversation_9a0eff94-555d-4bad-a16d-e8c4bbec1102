<template>
  <div class="exam-action-container">
    <div class="container-header">
      <div class="left">
        <span>考试--{{ typeMap[type]?.label }}</span>
      </div>
      <div class="right">
        <div class="btn primary-btn" @click="handleBack">返回</div>
      </div>
    </div>

    <div class="content">
      <div class="left-content">
        <el-scrollbar
          style="width: 100%; height: 100%"
          warp-style="overflow-x: hidden;"
        >
          <div class="block" :style="{ pointerEvents: 'none' }">
            <div class="label-title">基础信息</div>
            <el-row>
              <el-col :span="24" align="center">
                <div class="label">考试标题</div>
                <div class="input">
                  <el-input
                    v-model="examForm.name"
                    placeholder="请输入考试标题"
                    clearable
                    size="large"
                    maxlength="20"
                    show-word-limit
                  />
                </div>
              </el-col>
            </el-row>
            <el-col :span="24" align="center">
              <div class="label">考试说明</div>
              <div class="input">
                <el-input
                  v-model="examForm.remark"
                  placeholder="请输入考试说明"
                  clearable
                  size="large"
                  maxlength="200"
                  show-word-limit
                />
              </div>
            </el-col>

            <el-row>
              <el-col :span="24" align="center">
                <div class="label">关联课程</div>
                <div class="input">
                  <el-select
                    size="large"
                    v-model="examForm.content_id"
                    placeholder="请选择关联课程"
                    filterable
                    clearable
                    :teleported="false"
                    :suffix-icon="`CaretBottom`"
                  >
                    <el-option
                      v-for="item in linkCourseOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </div>
              </el-col>
            </el-row>
          </div>

          <div class="block" :style="{ pointerEvents: 'none' }">
            <div class="label-title">考试规则</div>
            <el-row>
              <el-col :span="24" align="center">
                <div class="label"><span>*</span>考试时间</div>
                <div class="input">
                  <el-radio-group v-model="examForm.time_type">
                    <el-radio :value="10">不限制时间</el-radio>
                    <el-radio :value="20">完成课程学习</el-radio>
                  </el-radio-group>
                </div>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24" align="center">
                <div class="label">考试次数</div>
                <div class="input">
                  <el-input
                    v-model="examForm.reretry_times"
                    placeholder="请输入 "
                    clearable
                    size="large"
                    class="input-with-inner-append"
                  >
                    <template #append>次</template>
                  </el-input>
                </div>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24" align="center">
                <div class="label"><span>*</span>考试分钟</div>
                <div class="input">
                  <el-input
                    v-model="examForm.time_limit"
                    placeholder="请输入 "
                    clearable
                    size="large"
                    class="input-with-inner-append"
                  >
                    <template #append>分钟</template>
                  </el-input>
                </div>
              </el-col>
            </el-row>
          </div>
          <!-- 成绩等级 -->
          <div class="block" :style="{ pointerEvents: 'none' }">
            <div class="block-header">
              <div class="label-title">成绩等级</div>
            </div>
            <el-row>
              <div class="grade-level">
                <div
                  class="grade-item"
                  v-for="(item, index) in gradesOptions"
                  :key="index"
                >
                  <div
                    class="grade-name"
                    :style="{ background: item.background }"
                  >
                    {{ item.label }}
                  </div>
                  <div class="input">
                    <el-input
                      v-model="examForm.result_grades[index][item.label]"
                      placeholder="请输入 "
                      clearable
                      size="large"
                      class="input-with-inner-append"
                    >
                      <template #append>分</template>
                    </el-input>
                  </div>
                </div>
              </div>
            </el-row>
          </div>
        </el-scrollbar>
      </div>

      <div class="right-content">
        <div class="block">
          <div class="block-header">
            <div class="label-title">考试</div>
          </div>

          <el-row>
            <el-col :span="24" align="center">
              <div class="label">满分</div>
              <div class="input">
                <el-input
                  v-model="examForm.score_set"
                  placeholder="请输入"
                  clearable
                  size="large"
                  maxlength="20"
                  show-word-limit
                />
              </div>
            </el-col>
          </el-row>
          <!-- 试卷列表 -->
          <el-row>
            <div class="label">总共{{ paperList.length }}套试卷</div>
            <div class="paper-list">
              <el-scrollbar
                style="width: 100%; height: 100%"
                warp-style="overflow-x: hidden;"
              >
                <div
                  class="paper-item"
                  v-for="(item, index) in paperList"
                  :key="index"
                >
                  <div class="left-text">
                    <div class="paper-item-name">
                      {{ item.name }}
                    </div>
                    <div class="paper-item-info">
                      <span class="paper-item-score">
                        · 满分 {{ item.score_set }}分</span
                      >
                      <!-- <span class="paper-item-count" v-if="item.question_count">
                      | {{ item.question_count }}题
                    </span>
                    <span
                      class="paper-item-topics"
                      v-if="item.topics && item.topics.length > 0"
                    >
                      | {{ item.topics.map((t) => t.name).join("、") }}
                    </span> -->
                    </div>
                  </div>
                  <div class="right-btn">
                    <div
                      class="btn primary-btn"
                      @click="handleExam('preview', item, index)"
                    >
                      预览
                    </div>
                  </div>
                </div>
              </el-scrollbar>
            </div>
          </el-row>
        </div>
        <div class="block">
          <div class="block-header">
            <div class="label-title">考试情况</div>
          </div>
          <div class="exam-info">
            <el-table :data="gradesData" border fit highlight-current-row>
              <el-table-column label="优秀" align="center" min-width="50">
                <template #default="scope">
                  <span> {{ scope.row.excellent }}</span>
                </template>
              </el-table-column>
              <el-table-column label="良好" align="center" min-width="50">
                <template #default="scope">
                  <span> {{ scope.row.good }}</span>
                </template>
              </el-table-column>

              <el-table-column label="及格" align="center" min-width="50">
                <template #default="scope">
                  <span> {{ scope.row.pass }}</span>
                </template>
              </el-table-column>
              <el-table-column label="不及格" align="center" min-width="50">
                <template #default="scope">
                  <span> {{ scope.row.fail }}</span>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </div>

    <!-- 试卷预览弹窗 -->
    <el-dialog
      class="preview-dialog"
      v-model="previewDialog.visible"
      :title="previewDialog.title"
      :width="previewDialog.width"
      append-to-body
      @close="closePreviewDialog"
    >
      <template #header>
        <div class="dialog-title">
          {{ previewDialog.title }}
        </div>
      </template>
      <div class="dialog-body">
        <el-scrollbar height="100%" warp-style="overflow-x: hidden;">
          <div class="paper-preview-header">
            <div class="title">{{ previewPaperInfo.name || "单元测试" }}</div>
            <div class="description">
              <span
                >共 {{ previewPaperInfo.total }} 题 | 总分
                {{ previewPaperInfo.scores }} 分 | 限时
                {{ previewPaperInfo.time_limit }} 分钟</span
              >
            </div>
          </div>
          <div class="paper-preview-container">
            <ExamPaper
              :isOperable="false"
              :showAnalysis="true"
              :showAnswerType="'all'"
              :showSelectedAnswer="false"
              :showQuestionScore="true"
              :itemStyle="'shadowBorder'"
              :showAllAnswer="true"
              :show-get-score="false"
              :show-answer-time="false"
            />
            <!--    :paperData="[]" -->
          </div>
        </el-scrollbar>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <div class="btn primary-btn" @click="closePreviewDialog">确 定</div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { useRoute, useRouter } from "vue-router";

import { getExamDetail, addExam, updateExam, getExamPapers } from "@/api/exam";
import { getCourses } from "@/api/course";
import ExamPaper from "./components/ExamPaper.vue";

defineOptions({
  name: "ExamAction",
  inheritAttrs: false,
});

const route = useRoute();
const router = useRouter();

const type: any = route.query.type;
const examId: any = route.query.id;

const typeMap = reactive<any>({
  create: { value: "create", label: "新增" },
  edit: { value: "edit", label: "修改" },
  detail: { value: "detail", label: "详情" },
});

const gradesOptions = ref<any>([
  {
    value: "",
    label: "优秀",
    en: "excellent",
    background: "linear-gradient( 180deg, #33CEC9 0%, #1C8D84 100%) ",
  },
  { value: "", label: "良好", en: "good", background: "#0DB582" },
  {
    value: "",
    label: "及格",
    en: "pass",
    background: "linear-gradient( 180deg, #84A395 0%, #A6BFB0 100%)",
  },
  { value: "", label: "不及格", en: "fail", background: " #3B4664" },
  // { value: '', label: "未完成", en: "undone" },
]);

// 考试表单数据
const examForm = reactive<any>({
  name: "",
  enabled: true, //	否	Boolean	是否启用，默认：否
  is_compuls: false, //	否	Boolean	是否启用考试，默认：否
  remark: "", //	否	String	考试说明
  content_id: "", //	是	Integer	关联内容的id
  content_type: "Course", //	是	String	内容类型，课程-Course，章节-Chapter
  time_type: 10, //	否	Integer	考试的时间类型，10-不限制时间，20-完成内容后（课程或章节）
  reretry_times: "", //	否	Integer	允许的重考次数，默认：0，即不能重考
  time_limit: "", //	否	Integer	考试时间限制，单位：分钟，默认：60分钟
  score_set: "", //	是	Integer	考试匹配的总分限制，默认：100分
  result_grades: [{ 优秀: 90 }, { 良好: 80 }, { 及格: 60 }, { 不及格: 0 }],
  //  //	是	String	考试成绩等级配置，json格式，如：[{"优秀": 90}, {"良好": 80}, {"及格": 60}, {"不及格": 0}]，分数是右闭区间的结构，比如：及格就是(79, 60]
  paper_ids: [], //	是	Array	试卷id列表，如：[1, 2]
});

const paperList = ref<any>([]);
const linkCourseOptions = ref<any>([]);
const gradesData = reactive<any>([
  { good: 10, fail: 10, excellent: 10, pass: 20 },
]);
// {
//   // 不及格，及格，良好，优秀
//   及格: {
//     label: "及格",
//     count: "10",
//   },
//   不及格: {
//     label: "不及格",
//     count: "10",
//   },
//   优秀: {
//     label: "优秀",
//     count: "10",
//   },
//   良好: {
//     label: "良好",
//     count: "20",
//   },
// }

const previewDialog = reactive<any>({
  visible: false,
  type: "preview",
  width: "45%",
  title: "试卷预览",
});
const previewPaperInfo = reactive<any>({
  scores: 120,
  time_limit: 30,
  total: 4,
  name: "",
  questions: [],
});

onBeforeMount(() => {
  // getExamData();
});

onMounted(() => {});

async function getExamData() {
  try {
    const response = await getExamDetail(examId);
    if (response.data) {
      const examData = response.data;
      Object.assign(examForm, {
        name: examData.name || "",
        enabled: examData.enabled !== undefined ? examData.enabled : true,
        is_compuls: examData.is_compuls || false,
        remark: examData.remark || "",
        content_id: examData.content_id || "",
        content_type: examData.content_type || "Course",
        time_type: examData.time_type || 10,
        reretry_times: examData.reretry_times || 0,
        time_limit: examData.time_limit || 60,
        score_set: examData.score_set || 100,
        result_grades: examData.result_grades || [
          { 优秀: 90 },
          { 良好: 80 },
          { 及格: 60 },
          { 不及格: 0 },
        ],
        paper_ids: examData.paper_ids || [],
      });

      if (examData.papers && examData.papers.length > 0) {
        paperList.value = examData.papers.map((paper: any) => ({
          id: paper.id,
          name: paper.name,
          score_set: paper.total_score || paper.score_set,
          question_count: paper.question_count,
          topics: paper.topics || [],
        }));
      }
    }
  } catch (error) {
    console.error("获取考试数据失败:", error);
    ElMessage.error("获取考试数据失败");
  }
}

function handleExam(type: any, row?: any, index?: any) {
  if (type == "preview") {
    previewDialog.visible = true;
    if (row) {
      previewPaperInfo.name = row.name;
      previewPaperInfo.question_count = row.question_count;
      previewPaperInfo.total_score = row.total_score;
      previewPaperInfo.questions = row.questions || [];
    }
  }
}

function closePreviewDialog() {
  previewDialog.visible = false;
}

function handleBack() {
  router.go(-1);
}
</script>

<style scoped lang="scss">
.exam-action-container {
  height: 95%;
  margin: 20px;

  .container-header {
    display: flex;
    justify-content: space-between;
    padding: 20px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 3px 20px 1px rgb(109 125 126 / 7%);

    .left {
      display: flex;
      align-items: center;
      font-size: 20px;
      font-weight: 500;
      color: #3b4664;
    }

    .right {
      .btn {
        width: 52px;
        height: 28px;
        font-size: 13px;
        font-weight: 400;
        border-radius: 13px;
      }
    }
  }

  .content {
    position: relative;
    width: 100%;
    height: calc(100% - 70px);
    padding: 10px 0;
    display: flex;
    justify-content: space-between;
    .left-content {
      height: 100%;
      width: 804px;
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 3px 20px 1px rgb(109 125 126 / 7%);
      display: flex;
      flex-direction: column;
    }
    .right-content {
      height: 100%;
      width: calc(100% - 820px);
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 3px 20px 1px rgb(109 125 126 / 7%);
      display: flex;
      flex-direction: column;
    }
    .block {
      width: 100%;
      padding: 20px 30px;
      margin-bottom: 10px;
      border-bottom: 6px solid #edeeee;
      &:last-child {
        margin-bottom: 0px;
        border-bottom: none;
      }
      .el-row {
        margin: 20px 0;
      }

      .el-col {
        display: flex;
        align-items: center;
        // padding: 0 20px;
      }
      .block-header {
        display: flex;
        align-items: center;
      }
      .add-paper-action {
        display: flex;
        align-items: center;
      }

      .label {
        display: flex;
        align-items: center;
        // justify-content: center;
        width: 100px;
        font-size: 15px;
        font-weight: 400;
        color: #3b4664;

        span {
          color: red;
        }
      }

      .label-title {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        font-weight: 500;
        font-size: 18px;
        color: #3b4664;
      }

      .input {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        width: 100%;

        span {
          margin-top: 10px;
          font-size: 13px;
          font-weight: 400;
          color: #8d9295;
          text-align: left;
        }

        :deep(.el-date-editor) {
          width: 100% !important;
        }
      }

      .col-btn {
        display: flex;
        justify-content: flex-end;
        height: 40px;
      }

      .text-plain-btn {
        width: 124px;
        height: 38px;
      }

      .text-btn {
        margin-left: 40px;
        font-size: 16px;
        font-weight: 400;
        width: 90px;
        height: 38px;
        background: #ffffff;
        box-shadow: 0px 3px 6px 1px rgba(107, 174, 175, 0.25);
        border-radius: 19px 19px 19px 19px;
        border: 1px solid #6aaeae;

        font-weight: 400;
        font-size: 15px;
        color: #6aaeae;
      }

      .paper-list {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 90%;
        padding: 0 30px;
        box-shadow: inset 3px 3px 6px 1px rgba(188, 198, 214, 0.25);
        border-radius: 8px 8px 8px 8px;
        border: 1px solid #edeff4;
        height: 325px;
        margin-left: 100px;

        .paper-item {
          display: flex;
          align-items: center;
          justify-content: space-between;
          width: 100%;
          height: 60px;
          padding: 10px 25px;
          margin-bottom: 5px;
          background: linear-gradient(180deg, #eaf8e5 0%, #e2f8e2 100%);
          border: 1px solid #fff;
          border-radius: 13px;
          box-shadow: 0 0 6px 1px rgb(191 226 206 / 16%);

          .left-text,
          .right-btn {
            display: flex;
            align-items: center;
            font-weight: 400;
            font-size: 15px;
            color: #3b4664;

            .btn {
              width: 72px;
              height: 28px;
              margin-left: 10px;
              font-size: 13px;
              font-weight: 400;
              border-radius: 3px;
            }
          }

          .paper-item-name {
            // font-size: 16px;
            // font-weight: 500;
            // color: #3b4664;
            font-weight: 400;
            font-size: 15px;
            color: #3b4664;
          }

          .paper-item-info {
            font-size: 13px;
            color: #666;
            display: flex;
            align-items: center;
            flex-wrap: wrap;

            .paper-item-score {
              color: #1c8d84;
              font-weight: 500;
            }

            .paper-item-count,
            .paper-item-topics {
              margin-left: 5px;
            }
          }
        }
      }

      .grade-level {
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        align-items: center;
        width: 100%;
        padding-left: 30px;

        .grade-item {
          display: flex;
          align-items: center;
          margin-bottom: 20px;
          width: 100%;

          .grade-name {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 130px;
            height: 40px;
            border-radius: 8px 0px 0px 8px;
            padding: 0 10px;
            font-size: 15px;
            font-weight: 400;
            color: #ffff;
          }

          .input {
            width: 100%;
          }
        }
      }
      .exam-info {
        margin-top: 10px;
      }
    }
  }
}
</style>

<style lang="scss">
.preview-dialog {
  padding: 0 !important;

  .el-dialog__header {
    height: 88px;
    text-align: center;
    background: url("@/assets/images/dialog-header-green2.png") no-repeat;
    background-size: 100% 100%;

    .dialog-title {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      font-size: 24px;
      font-weight: 500;
      color: #fff;
    }
  }

  .el-dialog__body {
    padding: 20px !important;
  }

  .el-dialog__footer {
    padding: 20px !important;
  }

  .el-dialog__close {
    width: 21px;
    height: 21px;
    background: #fff;
    border-radius: 50%;
    svg {
      font-size: 20px;
      path {
        fill: #00918c !important;
      }
    }
  }

  .dialog-body {
    height: 65vh;
  }
  .paper-preview-header {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    margin-bottom: 10px;
    .title {
      font-weight: 500;
      font-size: 24px;
      color: #3b4664;
    }
    .description {
      font-weight: 400;
      font-size: 18px;
      color: #3b4664;
    }
  }
}
.exam-paper-dialog {
  padding: 0 !important;

  .el-dialog__header {
    height: 88px;
    text-align: center;
    background: url("@/assets/images/dialog-header-green2.png") no-repeat;
    background-size: 100% 100%;

    .dialog-title {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      font-size: 24px;
      font-weight: 500;
      color: #fff;
    }
  }

  .el-dialog__body {
    padding: 20px !important;
  }

  .el-dialog__footer {
    padding: 20px !important;
  }

  .el-dialog__close {
    width: 21px;
    height: 21px;
    background: #fff;
    border-radius: 50%;
    svg {
      font-size: 20px;
      path {
        fill: #00918c !important;
      }
    }
  }

  .dialog-body {
    height: 65vh;
  }

  .dialog-content-header {
    margin-bottom: 20px;
    display: flex;
    align-items: center;

    .filter-row {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 260px;
      margin-left: 20px;

      &:nth-child(1) {
        width: 260px;
        margin-left: 0;
      }
    }
    .btn {
      width: 116px;
      height: 40px;
      margin-left: 20px;
    }
  }

  .dialog-content-table {
    height: calc(100% - 120px);
    margin-bottom: 20px;

    .paper-name {
      font-weight: 500;
      color: #3b4664;
    }

    .topic-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 5px;
    }

    .cover-img {
      width: 40px;
      height: 30px;
      object-fit: cover;
      border-radius: 4px;
      margin-right: 10px;
    }
  }

  .dialog-content-footer {
    display: flex;
    justify-content: center;
    padding: 10px 0;
  }

  .dialog-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .left {
      display: flex;
      align-items: center;
      span {
        padding: 2px 0;
        font-size: 20px;
        font-weight: 500;
        color: #00918c;
      }
    }

    .right {
      display: flex;
      align-items: center;
    }
  }
}
</style>
