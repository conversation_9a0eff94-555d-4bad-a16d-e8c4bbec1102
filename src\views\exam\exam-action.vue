<template>
  <div class="exam-action-container">
    <div class="container-header">
      <div class="left">
        <span>考试--{{ typeMap[type]?.label }}考试</span>
      </div>
      <div class="right">
        <div class="btn primary-btn" @click="handleBack">返回</div>
      </div>
    </div>

    <div class="content">
      <el-scrollbar height="100%" warp-style="overflow-x: hidden;">
        <div class="block">
          <div class="label-title">基础信息</div>
          <el-row>
            <el-col :span="12" align="center">
              <div class="label"><span>*</span>考试标题</div>
              <div class="input">
                <el-input
                  v-model="examForm.name"
                  placeholder="请输入考试标题"
                  clearable
                  size="large"
                  maxlength="20"
                  show-word-limit
                />
              </div>
            </el-col>
            <el-col :span="12" align="center">
              <div class="label"><span></span>考试说明</div>
              <div class="input">
                <el-input
                  v-model="examForm.name"
                  placeholder="请输入考试说明"
                  clearable
                  size="large"
                  maxlength="20"
                  show-word-limit
                />
              </div>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="12" align="center">
              <div class="label"><span>*</span>关联课程</div>
              <div class="input">
                <el-select
                  size="large"
                  v-model="examForm.courses"
                  placeholder="请选择"
                  filterable
                  clearable
                  multiple
                  :teleported="false"
                  :suffix-icon="`CaretBottom`"
                >
                  <el-option
                    v-for="item in linkCourseOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </div>
            </el-col>
          </el-row>
        </div>

        <div class="block">
          <div class="label-title">考试规则</div>
          <el-row>
            <el-col :span="12" align="center">
              <div class="label">
                <span>*</span>考试时间
                <el-tooltip
                  content="1.必选、单选，默认选中【不限制时间】
                           2.不限制时间：课程有效期内可随时进行考试；
                           3.完成课程学习：完成关联课程学习后解锁考试。"
                  placement="top-start"
                  popper-class="custom-tooltip-popper"
                >
                  <svg-icon icon-class="tips" />
                </el-tooltip>
              </div>
              <div class="input">
                <el-radio-group v-model="examForm.has_time">
                  <el-radio :value="false">不限制时间</el-radio>
                  <el-radio :value="true">完成课程学习</el-radio>
                </el-radio-group>
              </div>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12" align="center">
              <div class="label"><span>*</span>考试次数</div>
              <div class="input">
                <el-input
                  v-model="examForm.count"
                  placeholder="请输入 "
                  clearable
                  size="large"
                >
                  <template #append>次数</template>
                </el-input>
              </div>
            </el-col>
            <el-col :span="12" align="center">
              <div class="label"><span>*</span>考试分钟</div>
              <div class="input">
                <el-input
                  v-model="examForm.count"
                  placeholder="请输入 "
                  clearable
                  size="large"
                >
                  <template #append>分钟</template>
                </el-input>
              </div>
            </el-col>
          </el-row>
        </div>
        <div class="block">
          <div class="block-header">
            <div class="label-title">考试</div>
            <div class="add-paper-action">
              <div class="btn text-btn" @click="openPaperDialog">添加试卷</div>
              <div class="text-tips">
                <el-tooltip
                  content="1.只添加一套试卷，则该试卷为固定试卷；
                           2.添加多套卷面分一致的试卷，系统随机发放试卷；
                          3.多套试卷卷面分值不一致，显示【选择试卷卷面分】，选项来源为当前考试的所有试卷卷面分（过滤掉重复分值），将符合条件的试卷按规则1/2发放"
                  placement="top-start"
                  popper-class="custom-tooltip-popper"
                >
                  试卷发放规则说明
                </el-tooltip>
              </div>
            </div>
          </div>

          <el-row>
            <el-col :span="24" align="left">
              <div class="score-tips">
                <span>试卷卷面分值不一致，选择发放试卷分值（单位：分）</span>
                <div class="input">
                  <el-input
                    v-model="examForm.score_set"
                    placeholder="请输入"
                    clearable
                    size="large"
                  />
                </div>
              </div>
            </el-col>
          </el-row>

          <!-- 试卷列表 -->
          <el-row>
            <div class="paper-list">
              <div
                class="paper-item"
                v-for="(item, index) in paperList"
                :key="index"
              >
                <div class="left-text">
                  <div class="paper-item-name">
                    {{ item.name }}
                  </div>
                  <div class="paper-item-info">
                    <span class="paper-item-score"
                      >满分 {{ item.score_set }}分</span
                    >
                    <span class="paper-item-count" v-if="item.question_count">
                      | {{ item.question_count }}题
                    </span>
                    <span
                      class="paper-item-topics"
                      v-if="item.topics && item.topics.length > 0"
                    >
                      | {{ item.topics.map((t) => t.name).join("、") }}
                    </span>
                  </div>
                </div>
                <div class="right-btn">
                  <div
                    class="btn light-blue-btn"
                    @click="handleExam('preview', item, index)"
                  >
                    预览
                  </div>
                  <div
                    class="btn primary-btn"
                    @click="handleExam('edit', item, index)"
                  >
                    编辑
                  </div>
                  <div
                    class="btn orange-btn"
                    @click="handleExam('delete', item, index)"
                  >
                    删除
                  </div>
                </div>
              </div>
            </div>
          </el-row>
        </div>

        <!-- 成绩等级 -->
        <div class="block">
          <div class="block-header">
            <div class="label-title">成绩等级</div>
          </div>
          <el-row>
            <div class="grade-level">
              <div>
                <div
                  class="grade-item"
                  v-for="(item, index) in gradesOptions"
                  :key="index"
                >
                  <div class="grade-name">
                    {{ item.label }}
                  </div>
                  <div class="input">
                    <el-input
                      v-model="item.value"
                      placeholder="请输入 "
                      clearable
                      size="large"
                    >
                      <template #append>分</template>
                    </el-input>
                  </div>
                </div>
              </div>
            </div>
          </el-row>
        </div>
      </el-scrollbar>
      <div class="footer">
        <div class="btn cancel-btn" @click="handleBack">取 消</div>
        <div class="btn primary-btn" @click="saveExam">保 存</div>
      </div>
    </div>

    <!-- 试卷预览弹窗 -->
    <el-dialog
      class="preview-dialog"
      v-model="previewDialog.visible"
      :title="previewDialog.title"
      :width="previewDialog.width"
      append-to-body
      @close="closePreviewDialog"
    >
      <template #header>
        <div class="dialog-title">
          {{ previewDialog.title }}
        </div>
      </template>
      <div class="dialog-body">
        <el-scrollbar height="100%" warp-style="overflow-x: hidden;">
          <div class="paper-preview-header">
            <div class="title">{{ previewPaperInfo.name || "单元测试" }}</div>
            <div class="description">
              <span
                >共 {{ previewPaperInfo.total }} 题 | 总分
                {{ previewPaperInfo.scores }} 分 | 限时
                {{ previewPaperInfo.time_limit }} 分钟</span
              >
            </div>
          </div>
          <div class="paper-preview-container">
            <ExamPaper
              :isOperable="false"
              :showAnalysis="true"
              :showAnswerType="'all'"
              :showSelectedAnswer="false"
              :showQuestionScore="true"
              :itemStyle="'shadowBorder'"
              :showAnswer="true"
              :show-get-score="false"
              :show-answer-time="false"
              :paperData="[]"
            />
          </div>
        </el-scrollbar>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <div class="btn primary-btn" @click="closePreviewDialog">确 定</div>
        </div>
      </template>
    </el-dialog>

    <!-- 试卷选择弹窗 -->
    <el-dialog
      class="paperDialog"
      v-model="paperDialog.visible"
      :title="paperDialog.title"
      :width="paperDialog.width"
      append-to-body
      @close="closePaperDialog"
    >
      <template #header>
        <div class="dialog-title">
          {{ paperDialog.title }}
        </div>
      </template>
      <div class="dialog-body">
        <div class="dialog-content-header">
          <div class="filter-row">
            <el-input
              v-model="paperQueryParams.search"
              placeholder="请输入试卷名称"
              clearable
              size="large"
            />
          </div>
          <div class="filter-row">
            <el-select
              v-model="paperQueryParams.topic_id"
              placeholder="请选择主题"
              clearable
              size="large"
            >
              <el-option
                v-for="item in topicOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </div>
          <div class="btn primary-btn" @click="handlePaperQuery">
            <i-ep-search /> 搜索
          </div>
        </div>
        <div class="dialog-content-table">
          <el-table
            v-loading="paperLoading"
            element-loading-text="Loading"
            element-loading-background="#ffffffb4"
            :data="paperTableList"
            height="100%"
            border
            fit
            highlight-current-row
            ref="paperRef"
            @select="selectPaper"
            @select-all="selectAllPapers"
          >
            <el-table-column
              type="selection"
              align="center"
              min-width="20"
              class="dialog-checkbox2"
            />
            <el-table-column label="试卷名称" align="center" min-width="120">
              <template #default="scope">
                <div class="paper-name">
                  <span>{{ scope.row.name }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="关联主题" align="center" min-width="100">
              <template #default="scope">
                <div class="topic-tags">
                  <el-tag
                    v-for="topic in scope.row.topics"
                    :key="topic.id"
                    size="small"
                    type="success"
                    style="margin-right: 5px"
                  >
                    {{ topic.name }}
                  </el-tag>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="题目数量" align="center" min-width="80">
              <template #default="scope">
                <span>{{ scope.row.question_count || 0 }}题</span>
              </template>
            </el-table-column>
            <el-table-column label="总分" align="center" min-width="60">
              <template #default="scope">
                <span>{{ scope.row.total_score || 0 }}分</span>
              </template>
            </el-table-column>
            <el-table-column label="状态" align="center" min-width="60">
              <template #default="scope">
                <el-tag
                  :type="scope.row.enabled ? 'success' : 'info'"
                  size="small"
                >
                  {{ scope.row.enabled ? "启用" : "禁用" }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="创建时间" align="center" min-width="100">
              <template #default="scope">
                {{ formatDate(scope.row.created_at) }}
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="dialog-content-footer">
          <pagination
            v-if="paperTotal > 0"
            v-model:total="paperTotal"
            v-model:page="paperQueryParams.pageNum"
            v-model:limit="paperQueryParams.pageSize"
            @pagination="getPaperData"
          />
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <div class="left">
            <span>已选{{ selectedPapers?.length || 0 }}套试卷</span>
          </div>
          <div class="right">
            <div class="btn cancel-btn" @click="closePaperDialog">取 消</div>
            <div class="btn primary-btn" @click="handleSubmitPaper">确 定</div>
          </div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { useRoute, useRouter } from "vue-router";
import { ElMessage } from "element-plus";

import { getExamDetail, addExam, updateExam } from "@/api/exam";
import ExamPaper from "./components/ExamPaper.vue";
import Pagination from "@/components/Pagination/index.vue";

defineOptions({
  name: "ExamAction",
  inheritAttrs: false,
});

const route = useRoute();
const router = useRouter();

const type: any = route.query.type;
const examId: any = route.query.id;

const typeMap = reactive<any>({
  create: { value: "create", label: "新增" },
  edit: { value: "edit", label: "修改" },
  detail: { value: "detail", label: "详情" },
});
const gradesMap = reactive<any>({
  // 不及格，及格，良好，优秀
  10: { type: "info", label: "及格", color: "#909399" },
  20: { type: "primary", label: "良好", color: "#409eff" },
  30: { type: "success", label: "优秀", color: "#2ab7b0" },
  40: { type: "danger", label: "不及格", color: "#f56c6c" },
  // 30: { type: "warning", label: "已过期", color: "#e6a23c" },
});
const gradesOptions = ref<any>([
  { value: "", label: "优秀" },
  { value: "", label: "良好" },
  { value: "", label: "及格" },
  { value: "", label: "不及格" },
]);
// 考试表单数据
const examForm = reactive<any>({
  name: "",
  has_time: false,
  score_set: 100,
  duration: 60,
  passScore: 60,
  singleChoice: 0,
  multipleChoice: 0,
  trueOrFalse: 0,
  randomQuestions: false,
  shuffleOptions: false,
  allowRetake: false,
});
const paperList = ref<any>([]);
const linkCourseOptions = ref<any>([]);

const previewDialog = reactive<any>({
  visible: false,
  type: "preview",
  width: "45%",
  title: "试卷预览",
});
const previewPaperInfo = reactive<any>({
  scores: 120,
  time_limit: 30,
  total: 4,
});

// 试卷选择弹窗相关数据
const paperDialog = reactive<any>({
  visible: false,
  title: "选择试卷",
  width: "70%",
});

const paperQueryParams = reactive<any>({
  search: "",
  topic_id: "",
  pageNum: 1,
  pageSize: 10,
});

const paperTableList = ref<any>([]);
const paperTotal = ref(0);
const paperLoading = ref(false);
const selectedPapers = ref<any>([]);
const paperRef = ref<any>(null);

// 主题选项
const topicOptions = ref<any>([
  { value: "", label: "全部主题" },
  { value: "1", label: "安全知识" },
  { value: "2", label: "操作规范" },
  { value: "3", label: "应急处理" },
]);

onBeforeMount(() => {
  if (type === "edit" && examId) {
    loadExamData();
  }
});

onMounted(() => {
  // 初始化一些示例试卷数据
  if (paperList.value.length === 0) {
    paperList.value = [
      {
        id: 1,
        name: "示例试卷A",
        score_set: 100,
        question_count: 20,
        topics: [{ id: 1, name: "安全知识" }],
      },
    ];
  }
});

// 加载考试数据（编辑模式）
async function loadExamData() {
  try {
    const response = await getExamDetail(examId);
    if (response.data) {
      Object.assign(examForm, response.data);
    }
  } catch (error) {
    console.error("加载考试数据失败:", error);
    // 使用模拟数据作为后备
    Object.assign(examForm, {
      name: "安全知识考试",
      duration: 90,
      passScore: 70,
      singleChoice: 10,
      multipleChoice: 5,
      trueOrFalse: 5,
      randomQuestions: true,
      shuffleOptions: true,
      allowRetake: false,
    });
  }
}

function handleExam(type: any, row?: any, index?: any) {
  if (type == "add") {
    paperList.value.push({
      name: "12312321",
      score: 0,
      count: 0,
    });
  }
  if (type == "preview") {
    previewExamPaper();
    console.log("预览", row);
  }
  if (type == "edit") {
    console.log("编辑", row);
  }

  if (type == "delete") {
    paperList.value.splice(index, 1);
  }
}
// 保存考试
async function saveExam() {
  if (!examForm.name.trim()) {
    ElMessage.warning("请输入考试名称");
    return;
  }

  if (
    examForm.singleChoice + examForm.multipleChoice + examForm.trueOrFalse ===
    0
  ) {
    ElMessage.warning("请至少配置一种题型");
    return;
  }

  try {
    const examData = { ...examForm };

    if (type === "create") {
      await addExam(examData);
      ElMessage.success("创建成功");
    } else if (type === "edit") {
      await updateExam(examId, examData);
      ElMessage.success("保存成功");
    }

    // 保存成功后返回列表页
    setTimeout(() => {
      router.go(-1);
    }, 1000);
  } catch (error) {
    console.error("保存考试失败:", error);
    ElMessage.error("保存失败，请重试");
  }
}

// 预览考试
function previewExamPaper(row?: any) {
  previewDialog.visible = true;
}

function closePreviewDialog() {
  previewDialog.visible = false;
}

function handleBack() {
  router.go(-1);
}

// 试卷弹窗相关方法
function openPaperDialog() {
  paperDialog.visible = true;
  getPaperData();
}

function closePaperDialog() {
  paperDialog.visible = false;
  selectedPapers.value = [];
  paperQueryParams.search = "";
  paperQueryParams.topic_id = "";
  paperQueryParams.pageNum = 1;
}

// 获取试卷数据
async function getPaperData() {
  paperLoading.value = true;
  try {
    // 这里应该调用实际的API
    // const response = await getPapers(paperQueryParams);
    // paperTableList.value = response.data.list;
    // paperTotal.value = response.data.total;

    // 模拟数据
    const mockData = {
      list: [
        {
          id: 1,
          name: "安全知识测试卷A",
          topics: [
            { id: 1, name: "安全知识" },
            { id: 2, name: "操作规范" },
          ],
          question_count: 20,
          total_score: 100,
          enabled: true,
          created_at: "2024-01-15 10:30:00",
        },
        {
          id: 2,
          name: "操作规范考试卷",
          topics: [{ id: 2, name: "操作规范" }],
          question_count: 15,
          total_score: 75,
          enabled: true,
          created_at: "2024-01-14 14:20:00",
        },
        {
          id: 3,
          name: "应急处理能力测试",
          topics: [{ id: 3, name: "应急处理" }],
          question_count: 25,
          total_score: 125,
          enabled: false,
          created_at: "2024-01-13 09:15:00",
        },
        {
          id: 4,
          name: "综合能力测试卷",
          topics: [
            { id: 1, name: "安全知识" },
            { id: 2, name: "操作规范" },
            { id: 3, name: "应急处理" },
          ],
          question_count: 30,
          total_score: 150,
          enabled: true,
          created_at: "2024-01-12 16:45:00",
        },
      ],
      total: 4,
    };

    paperTableList.value = mockData.list;
    paperTotal.value = mockData.total;
  } catch (error) {
    console.error("获取试卷数据失败:", error);
    ElMessage.error("获取试卷数据失败");
  } finally {
    paperLoading.value = false;
  }
}

// 搜索试卷
function handlePaperQuery() {
  paperQueryParams.pageNum = 1;
  getPaperData();
}

// 选择试卷
function selectPaper(selection: any) {
  selectedPapers.value = selection;
}

// 全选试卷
function selectAllPapers(selection: any) {
  selectedPapers.value = selection;
}

// 提交选择的试卷
function handleSubmitPaper() {
  if (selectedPapers.value.length === 0) {
    ElMessage.warning("请至少选择一套试卷");
    return;
  }

  // 将选择的试卷添加到试卷列表
  selectedPapers.value.forEach((paper: any) => {
    const existingIndex = paperList.value.findIndex(
      (item: any) => item.id === paper.id
    );
    if (existingIndex === -1) {
      paperList.value.push({
        id: paper.id,
        name: paper.name,
        score_set: paper.total_score,
        question_count: paper.question_count,
        topics: paper.topics,
      });
    }
  });

  ElMessage.success(`成功添加${selectedPapers.value.length}套试卷`);
  closePaperDialog();
}

// 格式化日期
function formatDate(dateString: string) {
  if (!dateString) return "-";
  const date = new Date(dateString);
  return date.toLocaleDateString("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
  });
}
</script>

<style scoped lang="scss">
.exam-action-container {
  height: 95%;
  margin: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 3px 20px 1px rgb(109 125 126 / 7%);

  .container-header {
    display: flex;
    justify-content: space-between;
    padding: 20px;

    .left {
      display: flex;
      align-items: center;
      font-size: 20px;
      font-weight: 500;
      color: #3b4664;
    }

    .right {
      .btn {
        width: 52px;
        height: 28px;
        font-size: 13px;
        font-weight: 400;
        border-radius: 13px;
      }
    }
  }

  .content {
    position: relative;
    width: 100%;
    height: calc(100% - 140px);
    padding: 10px 20px;

    .block {
      width: 100%;
      padding: 10px;
      margin-bottom: 10px;
      border-bottom: 6px solid #edeeee;

      .el-row {
        margin: 20px 0;
      }

      .el-col {
        display: flex;
        align-items: center;
        // padding: 0 20px;
      }
      .block-header {
        display: flex;
        align-items: center;
      }
      .add-paper-action {
        display: flex;
        align-items: center;
      }

      .label {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 200px;
        font-size: 15px;
        font-weight: 400;
        color: #3b4664;

        span {
          color: red;
        }
      }

      .label-title {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        font-weight: 500;
        font-size: 18px;
        color: #3b4664;
      }

      .input {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        width: 100%;

        span {
          margin-top: 10px;
          font-size: 13px;
          font-weight: 400;
          color: #8d9295;
          text-align: left;
        }

        :deep(.el-date-editor) {
          width: 100% !important;
        }
      }

      .col-btn {
        display: flex;
        justify-content: flex-end;
        height: 40px;
      }

      .text-plain-btn {
        width: 124px;
        height: 38px;
      }

      .text-btn {
        margin-left: 40px;
        font-size: 16px;
        font-weight: 400;
        width: 90px;
        height: 38px;
        background: #ffffff;
        box-shadow: 0px 3px 6px 1px rgba(107, 174, 175, 0.25);
        border-radius: 19px 19px 19px 19px;
        border: 1px solid #6aaeae;

        font-weight: 400;
        font-size: 15px;
        color: #6aaeae;
      }

      .text-tips {
        margin-left: 10px;
        font-size: 12px;
        color: #1c8d84;
        cursor: pointer;

        &:hover {
          transform: scale(1.1);
        }
      }

      .score-tips {
        display: flex;
        align-items: center;
        padding-left: 30px;
        font-weight: 400;
        font-size: 15px;
        color: #3b4664;
        .input {
          width: 110px;
        }
      }

      .paper-list {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 100%;
        padding: 0 30px;

        .paper-item {
          display: flex;
          align-items: center;
          justify-content: space-between;
          width: 100%;
          height: 60px;
          padding: 10px 25px;
          margin-bottom: 5px;
          background: linear-gradient(180deg, #eaf8e5 0%, #e2f8e2 100%);
          border: 1px solid #fff;
          border-radius: 13px;
          box-shadow: 0 0 6px 1px rgb(191 226 206 / 16%);

          .left-text,
          .right-btn {
            display: flex;
            align-items: center;
            font-weight: 400;
            font-size: 15px;
            color: #3b4664;

            .btn {
              width: 72px;
              height: 28px;
              margin-left: 10px;
              font-size: 13px;
              font-weight: 400;
              border-radius: 3px;
            }
          }

          .paper-item-name {
            font-size: 16px;
            font-weight: 500;
            color: #3b4664;
            margin-bottom: 5px;
          }

          .paper-item-info {
            font-size: 13px;
            color: #666;
            display: flex;
            align-items: center;
            flex-wrap: wrap;

            .paper-item-score {
              color: #1c8d84;
              font-weight: 500;
            }

            .paper-item-count,
            .paper-item-topics {
              margin-left: 5px;
            }
          }
        }
      }

      .grade-level {
        display: flex;
        width: 100%;

        .label {
          align-items: flex-start;
          justify-content: flex-start;
          width: 150px;
          padding: 0 0 0 30px;
        }

        .grade-item {
          display: flex;
          align-items: center;
          margin-bottom: 10px;

          .grade-name {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100px;
            height: 42px;
            padding: 0 10px;
            font-size: 15px;
            font-weight: 400;
            color: #ffff;
            background: #1c8d84;
          }

          .input {
            width: 250px;
          }
        }
      }
    }

    .footer {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      width: 100%;
      padding: 15px 15px 0 0;

      .btn {
        width: 150px;
        height: 50px;
        margin-left: 20px;
        border-radius: 35px;

        &:nth-child(1) {
          margin-left: 0;
        }
      }
    }
  }
}
</style>

<style lang="scss">
.preview-dialog {
  padding: 0 !important;

  .el-dialog__header {
    height: 88px;
    text-align: center;
    background: url("@/assets/images/dialog-header-green2.png") no-repeat;
    background-size: 100% 100%;

    .dialog-title {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      font-size: 24px;
      font-weight: 500;
      color: #fff;
    }
  }

  .el-dialog__body {
    padding: 20px !important;
  }

  .el-dialog__footer {
    padding: 20px !important;
  }

  .el-dialog__close {
    width: 21px;
    height: 21px;
    background: #fff;
    border-radius: 50%;
    svg {
      font-size: 20px;
      path {
        fill: #00918c !important;
      }
    }
  }

  .dialog-body {
    height: 65vh;
  }
  .paper-preview-header {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    margin-bottom: 10px;
    .title {
      font-weight: 500;
      font-size: 24px;
      color: #3b4664;
    }
    .description {
      font-weight: 400;
      font-size: 18px;
      color: #3b4664;
    }
  }
}
.paperDialog {
  padding: 0 !important;

  .el-dialog__header {
    height: 88px;
    text-align: center;
    background: url("@/assets/images/dialog-header-green2.png") no-repeat;
    background-size: 100% 100%;

    .dialog-title {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      font-size: 24px;
      font-weight: 500;
      color: #fff;
    }
  }

  .el-dialog__body {
    padding: 20px !important;
  }

  .el-dialog__footer {
    padding: 20px !important;
  }

  .el-dialog__close {
    width: 21px;
    height: 21px;
    background: #fff;
    border-radius: 50%;
    svg {
      font-size: 20px;
      path {
        fill: #00918c !important;
      }
    }
  }

  .dialog-body {
    height: 65vh;
  }

  .dialog-content-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    gap: 15px;

    .filter-row {
      flex: 1;
      min-width: 200px;
    }

    .btn {
      width: 100px;
      height: 40px;
      flex-shrink: 0;
    }
  }

  .dialog-content-table {
    height: calc(100% - 120px);
    margin-bottom: 20px;

    .paper-name {
      font-weight: 500;
      color: #3b4664;
    }

    .topic-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 5px;
    }

    .cover-img {
      width: 40px;
      height: 30px;
      object-fit: cover;
      border-radius: 4px;
      margin-right: 10px;
    }
  }

  .dialog-content-footer {
    display: flex;
    justify-content: center;
    padding: 10px 0;
  }

  .dialog-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .left {
      font-size: 14px;
      color: #666;
    }

    .right {
      display: flex;
      gap: 15px;

      .btn {
        width: 80px;
        height: 36px;
        border-radius: 8px;
        font-size: 14px;
      }
    }
  }
}
</style>
