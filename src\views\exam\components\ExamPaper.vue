<template>
  <div class="exam-paper-container">
    <div class="questions">
      <component
        :is="questionMap[item.q_type].component"
        v-for="(item, index) in filteredQuestions"
        :key="index"
        :isOperable="isOperable"
        :showAnswer="showAnswer"
        :showSelectedAnswer="showSelectedAnswer"
        :showGetScore="showGetScore"
        :showQuestionScore="showQuestionScore"
        :showAnswerTime="showAnswerTime"
        :showAnalysis="showAnalysis"
        :showAnswerType="showAnswerType"
        :showCheckBox="showCheckBox"
        :showDrag="showDrag"
        :showDelete="showDelete"
        :itemStyle="itemStyle"
        :questionData="item"
        :questionIndex="index"
        :selectedQuestionIds="selectedQuestionIds"
        @update-answer="updateAnswer"
        @update-score="updateScore"
        @submit="submit"
        @handle-action="handleAction"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onBeforeMount, onMounted } from "vue";
import { useAppStore } from "@/store/modules/app";
import { useRoute, useRouter } from "vue-router";

import FillBlank from "./FillBlank.vue";
import Judgement from "./Judgement.vue";
import MultipleChoice from "./MultipleChoice.vue";
import SingleChoice from "./SingleChoice.vue";

//const { proxy } = getCurrentInstance();
// proxy 替代 this
defineOptions({
  name: "ExamPaper",
  inheritAttrs: false,
});

const props = defineProps({
  isOperable: {
    type: Boolean,
    default: false,
  }, //是否只读或答题
  showQuestionScore: {
    type: Boolean,
    default: false,
  }, //是否显示题目分数
  showAnswer: {
    type: Boolean,
    default: false,
  }, //是否显示答案
  showSelectedAnswer: {
    type: Boolean,
    default: false,
  }, //是否显示选中答案
  showGetScore: {
    type: Boolean,
    default: false,
  }, //是否显示得分
  showAnswerTime: {
    type: Boolean,
    default: false,
  }, //是否显示答题时间
  showCheckBox: {
    type: Boolean,
    default: false,
  }, //是否显示题目选中的选择框
  showAnalysis: {
    type: Boolean,
    default: false,
  }, //是否显示解析
  showAnswerType: {
    type: String,
    default: "all", // all-全部, correct-正确, incorrect-错误
  }, //筛选题目答完后答题类型
  itemStyle: {
    type: String,
    default: "bottomBorder", // shadowBorder-阴影边框, bottomBorder-底部边框，其它...
  }, //试题样式
  showDrag: {
    type: Boolean,
    default: false,
  }, //是否显示拖拽
  showDelete: {
    type: Boolean,
    default: false,
  }, //是否显示删除
  paperData: {
    type: Object,
    default: () => {},
  }, //试卷数据
  questionData: {
    type: Object,
    default: () => {
      return {};
    },
  }, //试题数据

  selectedQuestionIds: {
    type: Array,
    default: () => [],
  }, //外部传入的选中题目ID列表
  selectAllTrigger: {
    type: Boolean,
    default: false,
  }, //全选触发器
  selectNoneTrigger: {
    type: Boolean,
    default: false,
  }, //全不选触发器
});
const emit = defineEmits([
  "updateAnswer",
  "updateScore",
  "submit",
  "handleAction",
]);
const store = useAppStore();
const route = useRoute();
const router = useRouter();

const isOperable = computed(() => props.isOperable);
const showAnswer = computed(() => props.showAnswer);
const showSelectedAnswer = computed(() => props.showSelectedAnswer);
const showGetScore = computed(() => props.showGetScore);
const showQuestionScore = computed(() => props.showQuestionScore);
const showAnswerTime = computed(() => props.showAnswerTime);
const itemStyle = computed(() => props.itemStyle);
const showCheckBox = computed(() => props.showCheckBox);
const showDrag = computed(() => props.showDrag);
const showDelete = computed(() => props.showDelete);

const showAnalysis = computed(() => props.showAnalysis);
const showAnswerType = computed(() => props.showAnswerType);

const paperDataMock = ref<any>([
  {
    q_type: 10,
    title: "单选题",
    score: 10,
    questionNum: 4,
    answers: "A",
    content: {
      id: 1,
      q_type: 10,
      title: "中央空调开机时应按 (    ) 的先后顺序逐个启动，使机组投入运行。",
      options: [
        { A: "压缩机、冷冻泵、冷却泵、冷却塔" },
        { B: "压缩机、冷冻泵、冷却泵、冷却塔" },
        { C: "压缩机、冷冻泵、冷却泵、冷却塔" },
        { D: "压缩机、冷冻泵、冷却泵、冷却塔" },
      ],
      answers: "A",
      remark: "",
    },
  },
  {
    q_type: 10,
    title: "单选题",
    score: 10,
    questionNum: 4,
    answers: "A",
    content: {
      id: 1,
      q_type: 10,
      title: "有图片的   (    ) 的先后顺序逐个启动，使机组投入运行。",
      options: [
        { A: "压缩机", thumb: "https://img.yzcdn.cn/vant/cat.jpeg" },
        { B: "冷冻泵", thumb: "https://img.yzcdn.cn/vant/cat.jpeg" },
        { C: "冷却泵", thumb: "https://img.yzcdn.cn/vant/cat.jpeg" },
        { D: "冷却塔", thumb: "https://img.yzcdn.cn/vant/cat.jpeg" },
      ],

      answers: "A",
      remark:
        " 选项C错误，不能共用立管。选项D:新风机组焓差大室内机焓差小，其冷凝、蒸发压力不一致，不能共用",
    },
  },
  {
    q_type: 20,
    title: "多选题",
    score: 10,
    questionNum: 4,
    answers: "A,C,D",
    content: {
      id: 2,
      q_type: 20,
      title:
        "中央空调开机时应按 (    ) 的先后顺序逐个启动，使机组投入运行题目好长好长好长好长好长好长好长好长好长好长长翻行翻行翻行翻行翻行",
      options: [
        { A: "压缩机、冷冻泵、冷却泵、冷却塔" },
        { B: "压缩机、冷冻泵、冷却泵、冷却塔" },
        { C: "压缩机、冷冻泵、冷却泵、冷却塔" },
        { D: "压缩机、冷冻泵、冷却泵、冷却塔" },
      ],

      answers: "A,B",
      remark:
        " 选项C错误，不能共用立管。选项D:新风机组焓差大室内机焓差小，其冷凝、蒸发压力不一致，不能共用",
    },
  },
  {
    q_type: 30,
    title: "填空题",
    score: 10,
    questionNum: 0,
    answers: "露点2",
    content: {
      id: 4,
      q_type: 30,
      title: "中央空调开机时应按 (    ) 的先后顺序逐个启动，使机组投入运行。",
      answers: "露点",
      remark: "",
    },
  },
  {
    q_type: 40,
    title: "判断题",
    score: 10,
    questionNum: 2,
    answers: "A",
    content: {
      id: 4,
      q_type: 40,
      title: "干湿球温度越大，相对湿度也越大",
      options: [{ A: "对" }, { B: "错" }],
      answers: "A",
      remark:
        " 选项C错误，不能共用立管。选项D:新风机组焓差大室内机焓差小，其冷凝、蒸发压力不一致，不能共用",
    },
  },
]);

// const paperData = computed(() => props.paperData);
// const questionData = computed(() => props.questionData);
// const paperData = reactive<any>({
//   // id	:'',//Integer	记录id
//   name: "", //String	试卷名称
//   remark: "", //String	试卷说明
//   time_limit: "", //	Integer	试卷限时，单位：分钟
//   scores: "", //	Integer	总分
//   questions: [], //Array	试题列表
// });
// const questionData = reactive<any>({
//   id: "", //	Integer	试题id
//   name: "", //	String	试题题目
//   scores: "", //	Integer	得分
//   q_type: "", //Integer	试题类型，10-单选，20-多选，30-填空，40-判断
//   content: "", //	String	题目内容，当题目类型为选择题（p_type=10和20）时，为json格式的选项内容，如：[{"A": "xxx"}, {"B": "xxx"}]
//   answers: "", //String	题目的答案，有多个时用逗号隔开，如："A, B"
//   remark: "", //String	提示解析
// });
const questionMap = reactive<any>({
  10: { component: SingleChoice, label: "单选题" },
  20: { component: MultipleChoice, label: "多选题" },
  30: { component: FillBlank, label: "填空题" },
  40: { component: Judgement, label: "判断题" },
});

const filteredQuestions = computed(() => {
  const dataSource =
    props.paperData && Array.isArray(props.paperData)
      ? props.paperData
      : paperDataMock.value;

  if (showAnswerType.value === "all") {
    return dataSource;
  }
  // 假设每个题目有 userAnswer 字段? 且 answers 为标准答案
  return dataSource.filter((item: any) => {
    if (showAnswerType.value === "correct") {
      return item.answers === item.content.answers;
    }
    if (showAnswerType.value === "wrong") {
      return item.answers !== item.content.answers;
    }
    return true;
  });
});
const answersData = ref<any>([]);
const chosenQuestions = ref<any>([]);

// 监听全选触发器
watch(
  () => props.selectAllTrigger,
  (newVal) => {
    if (newVal) {
      selectAllQuestions();
    }
  }
);

// 监听全不选触发器
watch(
  () => props.selectNoneTrigger,
  (newVal) => {
    if (newVal) {
      selectNoneQuestions();
    }
  }
);

// 监听外部选中的题目ID列表
watch(
  () => props.selectedQuestionIds,
  (newIds) => {
    syncSelectedQuestions(newIds);
  },
  { deep: true }
);

function updateAnswer(payload: any) {
  emit("updateAnswer", payload);
}

function updateScore(payload: any) {
  emit("updateScore", payload);
}

function submit() {
  emit("submit");
}
function selectAllQuestions() {
  // 全选所有题目
  chosenQuestions.value = [...filteredQuestions.value];
  const params = {
    type: "select-all",
    chosenQuestions: chosenQuestions.value,
  };
  emit("handleAction", params);
}

function selectNoneQuestions() {
  // 全不选
  chosenQuestions.value = [];
  const params = {
    type: "select-none",
    chosenQuestions: chosenQuestions.value,
  };
  emit("handleAction", params);
}

function syncSelectedQuestions(selectedIds: any[]) {
  // 根据外部传入的ID列表同步选中状态
  chosenQuestions.value = filteredQuestions.value.filter((question: any) =>
    selectedIds.includes(question.content.id)
  );
}

function handleAction(payload: any) {
  // 根据payload判断各种emit操作
  if (payload.type === "select-question") {
    // 检查题目是否已经在选中列表中
    const existingIndex = chosenQuestions.value.findIndex(
      (q: any) => q.content.id === payload.question.content.id
    );
    if (existingIndex === -1) {
      chosenQuestions.value.push(payload.question);
    }
    const params = {
      type: "select-question",
      chosenQuestions: chosenQuestions.value,
    };
    emit("handleAction", params);
  } else if (payload.type === "remove-question") {
    // 从选中列表中移除题目
    const index = chosenQuestions.value.findIndex(
      (q: any) => q.content.id === payload.question.content.id
    );
    if (index > -1) {
      chosenQuestions.value.splice(index, 1);
    }
    const params = {
      type: "remove-question",
      chosenQuestion: payload.question,
    };
    emit("handleAction", params);
  }
}

onBeforeMount(() => {});
onMounted(() => {});
</script>

<style scoped lang="scss">
.exam-paper-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  .questions {
    width: 100%;
  }
}
</style>
