<template>
  <div class="exam-paper-container">
    <div class="left-sidebar">
      <!-- 试卷主题列表 -->
      <el-scrollbar
        style="width: 100%; height: 90%"
        warp-style="overflow-x: hidden;"
      >
        <div class="sidebar-list">
          <draggable
            v-model="sidebarTopics"
            class="drag-container"
            :item-key="'topic'"
            @end="endTopicsMove"
            :handle="'.sort'"
          >
            <template #item="{ element: item, index: idx }">
              <div
                class="green-item"
                :key="idx"
                @click="handleTopicClick('active', item)"
                :class="[item.id === activeTopic ? 'active-item' : '']"
              >
                <div class="green-title">
                  <div class="left">
                    <div class="icon sort">
                      <svg-icon icon-class="drag3" v-if="idx !== 0" />
                    </div>
                    <span class="text">{{ item.name }} </span>
                  </div>

                  <div class="right" v-if="idx !== 0">
                    <div
                      class="icon edit"
                      @click="handleTopicClick('edit', item)"
                    >
                      <svg-icon icon-class="edit2" />
                    </div>
                    <div
                      class="icon delete"
                      @click="handleTopicClick('delete', item)"
                    >
                      <svg-icon icon-class="delete" />
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </draggable>
        </div>
      </el-scrollbar>

      <div
        class="sidebar-footer"
        style="position: absolute; bottom: 20px; width: 90%"
      >
        <div class="btn text-btn" @click="handleTopicClick('create')">
          新增主题
        </div>
      </div>
    </div>
    <div class="right-content">
      <div class="container-header">
        <div class="left">
          <div class="filter-row">
            <el-input
              v-model="queryParams.search"
              placeholder="请输入试卷名称"
              clearable
              size="large"
            />
          </div>

          <div class="btn primary-btn" @click="handleQuery">
            <i-ep-search /> 搜索
          </div>
        </div>
        <div class="right">
          <div class="text-btn" @click="handleCreate">
            <i-ep-circle-plus style="margin-right: 5px" />
            新增试卷
          </div>
        </div>
      </div>

      <div class="content">
        <el-table
          v-loading="loading"
          element-loading-text="Loading"
          element-loading-background="#ffffffb4"
          :data="tableData"
          height="100%"
          border
          fit
          highlight-current-row
        >
          <el-table-column label="试卷名称" align="center" min-width="140">
            <template #default="scope">
              <div class="paper-name">
                <span>{{ scope.row.name }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="关联主题" align="center" min-width="120">
            <template #default="scope">
              <div class="topic-tags">
                <el-tag
                  v-for="topic in scope.row.topics"
                  :key="topic.id"
                  size="small"
                  type="success"
                >
                  {{ topic.name }}
                </el-tag>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="题目数量" align="center" min-width="80">
            <template #default="scope">
              <span style="color: #00918c"
                >{{ scope.row.question_count }}题</span
              >
            </template>
          </el-table-column>

          <el-table-column label="总分" align="center" min-width="60">
            <template #default="scope">
              <span style="color: #1c8d84; font-weight: 500"
                >{{ scope.row.total_score }}分</span
              >
            </template>
          </el-table-column>

          <el-table-column label="创建人" align="center" min-width="80">
            <template #default="scope">
              {{ scope.row.creator }}
            </template>
          </el-table-column>

          <el-table-column label="创建时间" align="center" min-width="120">
            <template #default="scope">
              {{ formatDate(scope.row.created_at) }}
            </template>
          </el-table-column>

          <el-table-column label="状态" align="center" min-width="80">
            <template #default="scope">
              <el-tag :type="scope.row.status === 10 ? 'success' : 'danger'">
                {{ scope.row.status === 10 ? "启用" : "禁用" }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="操作" align="center" min-width="280">
            <template #default="scope">
              <div class="option-btn">
                <div
                  class="btn light-blue-btn"
                  @click="onRowClick('preview', scope.row)"
                >
                  预览
                </div>
                <div
                  class="btn"
                  :class="[
                    scope.row.status == 10 || scope.row.status == 30
                      ? 'light-green-btn'
                      : 'info-btn',
                  ]"
                  @click="onRowClick('status', scope.row)"
                >
                  {{
                    scope.row.status == 10 || scope.row.status == 30
                      ? "启用"
                      : scope.row.status == 20
                      ? "停用"
                      : "--"
                  }}
                </div>

                <div
                  class="btn primary-btn"
                  @click="onRowClick('edit', scope.row)"
                >
                  编辑
                </div>
                <div
                  class="btn delete-btn"
                  @click="onRowClick('delete', scope.row)"
                >
                  删除
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="footer">
        <pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getData"
        />
      </div>
    </div>

    <!-- 试卷预览弹窗 -->
    <el-dialog
      class="preview-dialog"
      v-model="previewDialog.visible"
      :title="previewDialog.title"
      :width="previewDialog.width"
      append-to-body
      @close="closePreviewDialog"
    >
      <template #header>
        <div class="dialog-title">
          {{ previewDialog.title }}
        </div>
      </template>
      <div class="dialog-body">
        <el-scrollbar height="100%" warp-style="overflow-x: hidden;">
          <div class="paper-preview-header">
            <div class="title">{{ previewPaperInfo.name || "试卷预览" }}</div>
            <div class="description">
              <span
                >共 {{ previewPaperInfo.question_count }} 题 | 总分
                {{ previewPaperInfo.total_score }} 分</span
              >
            </div>
          </div>
          <div class="paper-preview-container">
            <ExamPaper
              :isOperable="false"
              :showAnalysis="true"
              :showAnswerType="'all'"
              :showSelectedAnswer="false"
              :showQuestionScore="true"
              :itemStyle="'shadowBorder'"
              :showAllAnswer="true"
              :show-get-score="false"
              :show-answer-time="false"
              :paperData="previewPaperInfo.questions || []"
            />
          </div>
        </el-scrollbar>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <div class="btn primary-btn" @click="closePreviewDialog">确 定</div>
        </div>
      </template>
    </el-dialog>

    <!-- 主题弹窗 -->
    <el-dialog
      v-model="topicDialog.visible"
      :title="topicDialog.title"
      :width="topicDialog.width"
      :before-close="closeTopicDialog"
      append-to-body
    >
      <div class="dialog-body">
        <el-form :model="topicForm" ref="topicFormRef">
          <el-form-item
            label="主题名称"
            prop="name"
            :rules="[
              { required: true, message: '请输入主题名称', trigger: 'blur' },
            ]"
          >
            <el-input
              v-model="topicForm.name"
              placeholder="请输入主题名称"
              maxlength="20"
              show-word-limit
              size="large"
              clearable
            />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <div class="btn cancel-btn" @click="closeTopicDialog">取 消</div>
          <div class="btn primary-btn" @click="saveTopic">保 存</div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import draggable from "vuedraggable";
import { useUserStore } from "@/store/modules/user";
import { status } from "nprogress";
import { useRoute, useRouter } from "vue-router";
import ExamPaper from "./components/ExamPaper.vue";

import {
  parseTime,
  resetReactiveObject,
  secondsToHoursAndMinutes,
  numberToChinese,
  numberToChinese2,
} from "@/utils";
import { checkUserPermission } from "@/utils/auth";
import {
  getExamPapers,
  getExamPaperDetail,
  updateExamPaper,
  deleteExamPaper,
} from "@/api/exam";

defineOptions({
  name: "ExamPaper",
  inheritAttrs: false,
});

const route = useRoute();
const router = useRouter();
const loading = ref(false);
const total = ref(0);

const queryParams = reactive({
  search: "",
  status: "",
  topic_id: "",
  pageNum: 1,
  pageSize: 20,
});

const activeTopic = ref<any>(0);
const sidebarTopics = ref<any>([
  {
    id: 0,
    name: "全部",
  },
  {
    id: 1,
    name: "安全知识",
  },
  {
    id: 2,
    name: "操作规范",
  },
  {
    id: 3,
    name: "应急处理",
  },
]);

// 试卷数据
const tableData = ref<any>([
  {
    id: 1,
    name: "安全知识测试卷A",
    topics: [
      { id: 1, name: "安全知识" },
      { id: 2, name: "操作规范" },
    ],
    question_count: 20,
    total_score: 100,
    creator: "管理员",
    created_at: "2024-01-15 10:30:00",
    status: 10,
    questions: [],
  },
  {
    id: 2,
    name: "操作规范考试卷",
    topics: [{ id: 2, name: "操作规范" }],
    question_count: 15,
    total_score: 75,
    creator: "张三",
    created_at: "2024-01-14 14:20:00",
    status: 10,
    questions: [],
  },
  {
    id: 3,
    name: "应急处理能力测试",
    topics: [{ id: 3, name: "应急处理" }],
    question_count: 25,
    total_score: 125,
    creator: "李四",
    created_at: "2024-01-13 09:15:00",
    status: 20,
    questions: [],
  },
  {
    id: 4,
    name: "综合能力测试卷",
    topics: [
      { id: 1, name: "安全知识" },
      { id: 2, name: "操作规范" },
      { id: 3, name: "应急处理" },
    ],
    question_count: 30,
    total_score: 150,
    creator: "王五",
    created_at: "2024-01-12 16:45:00",
    status: 10,
    questions: [],
  },
]);

// 预览弹窗
const previewDialog = reactive<any>({
  visible: false,
  type: "preview",
  width: "45%",
  title: "试卷预览",
});

const previewPaperInfo = reactive<any>({
  name: "",
  question_count: 0,
  total_score: 0,
  questions: [],
});

// 主题弹窗
const topicDialog = reactive<any>({
  visible: false,
  title: "新增主题",
  width: "400px",
});

const topicForm = reactive<any>({
  id: null,
  name: "",
});

const topicFormRef = ref<any>(null);

onBeforeMount(() => {
  getData();
});

onMounted(() => {});

// 格式化日期
function formatDate(dateString: string) {
  if (!dateString) return "-";
  const date = new Date(dateString);
  return date.toLocaleDateString("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
  });
}

function handleQuery() {
  queryParams.pageNum = 1;
  getData();
}

function handleCreate() {
  router.push({ path: "exam-paper-action", query: { type: "create" } });
}

function getData() {
  loading.value = true;

  // 模拟API调用
  setTimeout(() => {
    let filteredData = [...tableData.value];

    // 根据主题筛选
    if (activeTopic.value && activeTopic.value !== 0) {
      filteredData = filteredData.filter((item) =>
        item.topics.some((topic: any) => topic.id === activeTopic.value)
      );
    }

    // 根据搜索关键字筛选
    if (queryParams.search) {
      filteredData = filteredData.filter((item) =>
        item.name.toLowerCase().includes(queryParams.search.toLowerCase())
      );
    }

    // 根据状态筛选
    if (queryParams.status) {
      filteredData = filteredData.filter(
        (item) => item.status.toString() === queryParams.status
      );
    }

    total.value = filteredData.length;
    loading.value = false;
  }, 500);
}

function onRowClick(type: string, row: any) {
  switch (type) {
    case "preview":
      handlePreview(row);
      break;
    case "status":
      handleStatusChange(row);
      break;
    case "detail":
      router.push({
        path: "exam-paper-action",
        query: { id: row.id, type: "detail" },
      });
      break;
    case "edit":
      router.push({
        path: "exam-paper-action",
        query: { id: row.id, type: "edit" },
      });
      break;
    case "delete":
      handleDelete(row);
      break;
  }
}

// 预览试卷
function handlePreview(row: any) {
  previewPaperInfo.name = row.name;
  previewPaperInfo.question_count = row.question_count;
  previewPaperInfo.total_score = row.total_score;
  previewPaperInfo.questions = row.questions || [];
  previewDialog.visible = true;
}

function closePreviewDialog() {
  previewDialog.visible = false;
}

function handleStatusChange(row: any) {
  const newStatus = row.status === 10 ? 20 : 10;
  const statusText = newStatus === 10 ? "启用" : "禁用";

  ElMessageBox.confirm(`确定要${statusText}试卷"${row.name}"吗？`, "状态切换", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    row.status = newStatus;
    ElMessage.success(`${statusText}成功`);
  });
}

function handleDelete(row: any) {
  ElMessageBox.confirm(`确定要删除试卷"${row.name}"吗？`, "删除确认", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    const index = tableData.value.findIndex((item: any) => item.id === row.id);
    if (index > -1) {
      tableData.value.splice(index, 1);
    }
    ElMessage.success("删除成功");
    getData();
  });
}

function handleTopicClick(type: string, item?: any) {
  switch (type) {
    case "active":
      activeTopic.value = item.id;
      queryParams.topic_id = item.id;
      getData();
      break;
    case "create":
      topicDialog.title = "新增主题";
      topicForm.id = null;
      topicForm.name = "";
      topicDialog.visible = true;
      break;
    case "edit":
      topicDialog.title = "编辑主题";
      topicForm.id = item.id;
      topicForm.name = item.name;
      topicDialog.visible = true;
      break;
    case "delete":
      handleTopicDelete(item);
      break;
  }
}

function handleTopicDelete(item: any) {
  ElMessageBox.confirm(`确定要删除主题"${item.name}"吗？`, "删除确认", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    const index = sidebarTopics.value.findIndex(
      (topic: any) => topic.id === item.id
    );
    if (index > -1) {
      sidebarTopics.value.splice(index, 1);
    }
    ElMessage.success("删除成功");
    if (activeTopic.value === item.id) {
      activeTopic.value = 0;
      getData();
    }
  });
}

function saveTopic() {
  topicFormRef.value?.validate((valid: boolean) => {
    if (valid) {
      if (topicForm.id) {
        // 编辑
        const index = sidebarTopics.value.findIndex(
          (topic: any) => topic.id === topicForm.id
        );
        if (index > -1) {
          sidebarTopics.value[index].name = topicForm.name;
        }
        ElMessage.success("编辑成功");
      } else {
        // 新增
        const newTopic = {
          id: Date.now(),
          name: topicForm.name,
        };
        sidebarTopics.value.push(newTopic);
        ElMessage.success("新增成功");
      }
      closeTopicDialog();
    }
  });
}

function closeTopicDialog() {
  topicDialog.visible = false;
  topicForm.id = null;
  topicForm.name = "";
}

function endTopicsMove() {
  ElMessage.success("主题排序已更新");
}
</script>

<style scoped lang="scss">
.exam-paper-container {
  height: 95%;
  margin: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 3px 20px 1px rgb(109 125 126 / 7%);
  display: flex;

  .left-sidebar {
    width: 280px;
    min-width: 280px;
    height: 100%;
    background: #f8f9fa;
    border-right: 1px solid #e9ecef;
    position: relative;

    .sidebar-list {
      padding: 20px 15px;

      .green-item {
        margin-bottom: 10px;
        padding: 12px 15px;
        background: #fff;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s ease;
        border: 1px solid #e9ecef;

        &:hover {
          background: #f0f9ff;
          border-color: #00918c;
        }

        &.active-item {
          background: #00918c;
          border-color: #00918c;

          .green-title .text {
            color: #fff;
          }

          .icon svg {
            color: #fff;
          }
        }

        .green-title {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .left {
            display: flex;
            align-items: center;

            .icon {
              margin-right: 8px;
              cursor: move;

              svg {
                font-size: 16px;
                color: #666;
              }
            }

            .text {
              font-size: 14px;
              color: #333;
              font-weight: 500;
            }
          }

          .right {
            display: flex;
            gap: 5px;

            .icon {
              padding: 4px;
              border-radius: 4px;
              cursor: pointer;
              transition: all 0.2s ease;

              &:hover {
                background: rgba(255, 255, 255, 0.2);
              }

              svg {
                font-size: 14px;
                color: #666;
              }
            }
          }
        }
      }
    }

    .sidebar-footer {
      .btn {
        width: 100%;
        height: 40px;
        border-radius: 8px;
      }
    }
  }

  .right-content {
    flex: 1;
    display: flex;
    flex-direction: column;

    .container-header {
      display: flex;
      justify-content: space-between;
      width: 100%;
      padding: 20px;

      .left {
        display: flex;
        align-items: center;
        gap: 15px;

        .filter-row {
          width: 200px;
        }

        .btn {
          width: 100px;
          height: 40px;
        }
      }

      .right {
        display: flex;
        align-items: center;
      }
    }

    .content {
      flex: 1;
      padding: 0 20px;

      .paper-name {
        font-weight: 500;
        color: #3b4664;
      }

      .topic-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 5px;
      }
    }

    .footer {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20px;
    }
  }
}

// 主题弹窗样式
:deep(.el-dialog) {
  .el-dialog__header {
    padding: 20px 20px 10px;
    border-bottom: 1px solid #e9ecef;
  }

  .el-dialog__body {
    padding: 20px;
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    padding: 15px 20px 20px;

    .btn {
      width: 80px;
      height: 36px;
      border-radius: 6px;
      font-size: 14px;
    }
  }
}
</style>
<style lang="scss">
.preview-dialog {
  padding: 0 !important;

  .el-dialog__header {
    height: 88px;
    text-align: center;
    background: url("@/assets/images/dialog-header-green2.png") no-repeat;
    background-size: 100% 100%;

    .dialog-title {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      font-size: 24px;
      font-weight: 500;
      color: #fff;
    }
  }

  .el-dialog__body {
    padding: 20px !important;
  }

  .el-dialog__footer {
    padding: 20px !important;
  }

  .el-dialog__close {
    width: 21px;
    height: 21px;
    background: #fff;
    border-radius: 50%;
    svg {
      font-size: 20px;
      path {
        fill: #00918c !important;
      }
    }
  }

  .dialog-body {
    height: 65vh;
  }
  .paper-preview-header {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    margin-bottom: 10px;
    .title {
      font-weight: 500;
      font-size: 24px;
      color: #3b4664;
    }
    .description {
      font-weight: 400;
      font-size: 18px;
      color: #3b4664;
    }
  }
}
.exam-paper-dialog {
  padding: 0 !important;

  .el-dialog__header {
    height: 88px;
    text-align: center;
    background: url("@/assets/images/dialog-header-green2.png") no-repeat;
    background-size: 100% 100%;

    .dialog-title {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      font-size: 24px;
      font-weight: 500;
      color: #fff;
    }
  }

  .el-dialog__body {
    padding: 20px !important;
  }

  .el-dialog__footer {
    padding: 20px !important;
  }

  .el-dialog__close {
    width: 21px;
    height: 21px;
    background: #fff;
    border-radius: 50%;
    svg {
      font-size: 20px;
      path {
        fill: #00918c !important;
      }
    }
  }

  .dialog-body {
    height: 65vh;
  }

  .dialog-content-header {
    margin-bottom: 20px;
    display: flex;
    align-items: center;

    .filter-row {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 260px;
      margin-left: 20px;

      &:nth-child(1) {
        width: 260px;
        margin-left: 0;
      }
    }
    .btn {
      width: 116px;
      height: 40px;
      margin-left: 20px;
    }
  }

  .dialog-content-table {
    height: calc(100% - 120px);
    margin-bottom: 20px;

    .paper-name {
      font-weight: 500;
      color: #3b4664;
    }

    .topic-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 5px;
    }

    .cover-img {
      width: 40px;
      height: 30px;
      object-fit: cover;
      border-radius: 4px;
      margin-right: 10px;
    }
  }

  .dialog-content-footer {
    display: flex;
    justify-content: center;
    padding: 10px 0;
  }

  .dialog-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .left {
      display: flex;
      align-items: center;
      span {
        padding: 2px 0;
        font-size: 20px;
        font-weight: 500;
        color: #00918c;
      }
    }

    .right {
      display: flex;
      align-items: center;
    }
  }
}
</style>
