<template>
  <div class="exam-action-container">
    <div class="container-header">
      <div class="left">
        <span>题库--{{ questionDetail.name }} {{ typeMap[type].label }}</span>
      </div>
      <div class="right">
        <div class="btn primary-btn" @click="handleBack">返回</div>
      </div>
    </div>
    <div class="content">
      <el-scrollbar
        style="height: 100%; width: 100%"
        warp-style="overflow-x: hidden;"
        class="exam-scrollbar"
      >
        <div class="paper-info">
          <ExamPaper
            :isOperable="false"
            :questionData="questionDetail.question"
            :showAnalysis="true"
            :showAnswerType="'all'"
            :showSelectedAnswer="false"
            :showQuestionScore="true"
            :showAnswer="true"
            :show-get-score="false"
            :show-answer-time="false"
            :paperData="questionDetail"
          />
        </div>
      </el-scrollbar>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useUserStore } from "@/store/modules/user";
import { useRoute, useRouter } from "vue-router";
import {
  parseTime,
  resetReactiveObject,
  secondsToHoursAndMinutes,
  numberToChinese,
  numberToChinese2,
} from "@/utils";
import { checkUserPermission } from "@/utils/auth";
import ExamPaper from "./components/ExamPaper.vue";
import { getQuestionBankDetail } from "@/api/exam";

const { proxy } = getCurrentInstance() as any;
// proxy 替代 this
defineOptions({
  name: "ExamAction",
  inheritAttrs: false,
});
const userStore = useUserStore();
const route = useRoute();
const router = useRouter();

const type: any = route.query.type;
const questionBankId: any = route.query.id;
const typeMap = reactive<any>({
  create: { value: "create", label: "新增" },
  edit: { value: "edit", label: "修改" },
  detail: { value: "detail", label: "详情" },
});
const questionDetail = reactive<any>({
  name: "储能",
});

onBeforeMount(() => {});
onMounted(() => {});

function getQBDetail() {
  const params = {};
  getQuestionBankDetail(questionBankId.value)
    .then((res: any) => {
      questionDetail.name = res.name;
      questionDetail.question = res.paper;
    })
    .catch(() => {});
}

function handleBack() {
  router.go(-1);
}
</script>

<style scoped lang="scss">
.exam-action-container {
  height: 95%;
  margin: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 3px 20px 1px rgb(109 125 126 / 7%);

  .container-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;

    .left {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 20px;
      font-weight: 500;
      color: #3b4664;

      .tips {
        margin-left: 30px;
        font-size: 14px;
        font-weight: 400;
        color: #f23c33;
      }
    }

    .right {
      .btn {
        width: 52px;
        height: 28px;
        font-size: 13px;
        font-weight: 400;
        border-radius: 13px;
      }
    }
  }
  .content {
    width: 100%;
    height: calc(100% - 70px);
    padding: 10px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    .exam-scrollbar,
    .el-scrollbar__wrap {
      width: 100%;
    }
    :deep(.el-scrollbar__view) {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .paper-info {
      width: 95%;
      box-shadow: 0px 0px 6px 1px rgba(191, 226, 206, 0.16),
        inset 0px 0px 10px 1px rgba(19, 69, 65, 0.2);
      border-radius: 0px 0px 0px 0px;
      border: 1px solid #ffffff;
    }
  }
}
</style>
